#include "eqwidgetiem.h"
#include <qcombobox.h>
#include <qnamespace.h>
#include <QApplication>
#include <QSizePolicy>
#include <QSpacerItem>
#include <QHash>
#include <functional>
#include <QVariant>
#include "globalfont.h"

EqWidgetIem::EqWidgetIem(int index, QWidget* parent)
    : QFrame(parent)
    , mIndex(index)
    , mLayoutSpacing(0)
    , mLayoutMargins(0, 0, 0, 0)
    , mMinimumWidth(50)
    , mLabelStretch(2)
    , mComboStretch(1)
    , mGainStretch(3)
    , mFreqStretch(3)
    , mQStretch(3)
    , mCheckStretch(1)
    , mMainLayout(nullptr)
    , mItemLabel(nullptr)
    , mTypeComboBox(nullptr)
    , mGainDial(nullptr)
    , mFrequencyDial(nullptr)
    , mQValueDial(nullptr)
    , mEnabledCheckBox(nullptr)
{
    mData.type = "high pass";
    mData.gain = 0.0f;
    mData.frequency = 1000.0f;
    mData.qValue = 0.7f;
    mData.enabled = true;

    setupUI();
    setupConnections();
    setStyleSheet(
        "EqWidgetIem {"
        "    background-color: rgb(22,22,22);"
        "}"
        "QLabel {"
        "    color: #404040;"
        "}"
        "QCheckBox::indicator {"
        "    width: 20px;"
        "    height: 20px;"
        "    border-radius: 3px;"
        "}"
        "QCheckBox::indicator:checked {"
        "    background-color: #43cf7c;"
        "}"
        "QCheckBox::indicator:checked {"
        "    image:url(:/Icon/checkboxTick.svg);"
        "}"
        "QCheckBox::indicator:unchecked {"
        "    background-color: #43cf7c;"
        "}"
    );
}

EqWidgetIem::~EqWidgetIem()
{
}

void EqWidgetIem::setupUI()
{
    setMinimumWidth(100);

    mMainLayout = new QVBoxLayout(this);
    mMainLayout->setSpacing(mLayoutSpacing);
    mMainLayout->setContentsMargins(mLayoutMargins);

    mItemLabel = new QLabel(QString::number(mIndex + 1), this);
    mItemLabel->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Fixed);
    mItemLabel->setAlignment(Qt::AlignCenter);
    mMainLayout->addWidget(mItemLabel);

    mTypeComboBox = new ComboBoxS1M3(nullptr);
    mTypeComboBox->setIndicatorWHRatio(0.5);
    mTypeComboBox->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Fixed);
    QVector<QString> types = {
        "high pass",
        "Low pass",
        "High Shelf",
        "High Shelf"
    };
    mTypeComboBox->addItems(types);
    mMainLayout->addWidget(mTypeComboBox);
    QSpacerItem *spacer2 = new QSpacerItem(0, 0, QSizePolicy::Minimum, QSizePolicy::Expanding);
    mMainLayout->addSpacerItem(spacer2);

    mGainDial = new DialS1M5(this);
    mGainDial->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Fixed);
    mGainDial->setRange(-20.0f, 20.0f)
              .setValue(0.0f)
              .setStep(0.1f)
              .setPrecision(1)
              .showSign(true);
    mMainLayout->addWidget(mGainDial);
    QSpacerItem *spacer3 = new QSpacerItem(0, 0, QSizePolicy::Minimum, QSizePolicy::Expanding);
    mMainLayout->addSpacerItem(spacer3);

    mFrequencyDial = new DialS1M5(this);
    mFrequencyDial->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Fixed);
    mFrequencyDial->setRange(20.0f, 20000.0f)
                  .setValue(1000.0f)
                  .setStep(1.0f)
                  .setPrecision(0);
    mMainLayout->addWidget(mFrequencyDial);
    QSpacerItem *spacer4 = new QSpacerItem(0, 0, QSizePolicy::Minimum, QSizePolicy::Expanding);
    mMainLayout->addSpacerItem(spacer4);

    mQValueDial = new DialS1M5(this);
    mQValueDial->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Fixed);
    mQValueDial->setRange(0.1f, 10.0f)
                .setValue(0.7f)
                .setStep(0.1f)
                .setPrecision(1);
    mMainLayout->addWidget(mQValueDial);
    QSpacerItem *spacer5 = new QSpacerItem(0, 0, QSizePolicy::Minimum, QSizePolicy::Expanding);
    mMainLayout->addSpacerItem(spacer5);

    mEnabledCheckBox = new QCheckBox(this);
    mEnabledCheckBox->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Fixed);
    mEnabledCheckBox->setChecked(true);
    mMainLayout->addWidget(mEnabledCheckBox, 0, Qt::AlignCenter);
}

void EqWidgetIem::setupConnections()
{
    static QHash<EqWidgetItemData::ChangeFlag, std::function<void(const QVariant&)>> changeHandlers = {
        { EqWidgetItemData::TYPE_CHANGED, [this](const QVariant& arg) {
             mData.updateType(mTypeComboBox->currentText());
        }},
        { EqWidgetItemData::GAIN_CHANGED, [this](const QVariant& arg) {
            mData.updateGain(arg.toFloat());
        }},
        { EqWidgetItemData::FREQUENCY_CHANGED, [this](const QVariant& arg) {
            mData.updateFrequency(arg.toFloat());
        }},
        { EqWidgetItemData::QVALUE_CHANGED, [this](const QVariant& arg) {
            mData.updateQValue(arg.toFloat());
        }},
        { EqWidgetItemData::ENABLED_CHANGED, [this](const QVariant& arg) {
            mData.updateEnabled(arg.toBool());
        }}
    };
    auto* changeHandlersPtr = &changeHandlers;
    static auto handleAndEmit =[this, changeHandlersPtr](EqWidgetItemData::ChangeFlag flag, const QVariant& arg){
        changeHandlersPtr->value(flag)(arg);
        emit dataChanged(mIndex, mData);
        mData.clearAllChanged();
    };
    auto* handleAndEmitPtr = &handleAndEmit;

    connect(mTypeComboBox, &ComboBoxS1M3::currentIndexChanged, this, [handleAndEmitPtr](int index) {
        (*handleAndEmitPtr)(EqWidgetItemData::TYPE_CHANGED, QVariant(index));
    });
    connect(mGainDial, &DialS1M5::valueChanged, this, [handleAndEmitPtr](float value) {
        (*handleAndEmitPtr)(EqWidgetItemData::GAIN_CHANGED, QVariant(value));
    });
    connect(mFrequencyDial, &DialS1M5::valueChanged, this, [handleAndEmitPtr](float value) {
        (*handleAndEmitPtr)(EqWidgetItemData::FREQUENCY_CHANGED, QVariant(value));
    });
    connect(mQValueDial, &DialS1M5::valueChanged, this, [handleAndEmitPtr](float value) {
        (*handleAndEmitPtr)(EqWidgetItemData::QVALUE_CHANGED, QVariant(value));
    });
    connect(mEnabledCheckBox, &QCheckBox::toggled, this, [handleAndEmitPtr](bool enabled) {
        (*handleAndEmitPtr)(EqWidgetItemData::ENABLED_CHANGED, QVariant(enabled));
    });
}

EqWidgetIem& EqWidgetIem::setFont(QFont font)
{
    mFont = font;
    mTypeComboBox->setFont(mFont);
    mGainDial->setFont(mFont);
    mFrequencyDial->setFont(mFont);
    mQValueDial->setFont(mFont);
    resizeEvent(nullptr);
    return *this;
}

void EqWidgetIem::setSizeFactor(double sizeFactor)
{
    setFixedWidth(mMinimumWidth * sizeFactor);
    adjustFontAndSize();
}

void EqWidgetIem::setItemData(const EqWidgetItemData& data)
{
    mData = data;
    mTypeComboBox->setCurrentText(data.type);
    mGainDial->setValue(data.gain);
    mFrequencyDial->setValue(data.frequency);
    mQValueDial->setValue(data.qValue);
    mEnabledCheckBox->setChecked(data.enabled);
}

EqWidgetItemData EqWidgetIem::getItemData() const
{
    return mData;
}

void EqWidgetIem::setItemIndex(int index)
{
    mIndex = index;
    mItemLabel->setText(QString::number(index + 1));
}

void EqWidgetIem::setMinimumItemWidth(int width)
{
    mMinimumWidth = width;
    setMinimumWidth(width);
}

void EqWidgetIem::resizeEvent(QResizeEvent* event)
{
    QWidget::resizeEvent(event);
}

void EqWidgetIem::adjustFontAndSize()
{
    mEnabledCheckBox->setStyleSheet(QString(
    "QCheckBox::indicator {"
        "width: %1px;"
        "height: %1px;"
        "border-radius: %2px;"
        "}"
        "QCheckBox::indicator:checked {"
        "    background-color: #43cf7c;"
        "}"
        "QCheckBox::indicator:checked {"
        "    image:url(:/Icon/checkboxTick.svg);"
        "}"
        "QCheckBox::indicator:unchecked {"
        "    background-color: #43cf7c;"
        "}").arg(mEnabledCheckBox->height(), mEnabledCheckBox->height()*0.2)
    );
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, height()*0.08));
    mItemLabel->setFont(mFont);
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, height()*0.04));

    double perHeight = height()/15.0;
    mItemLabel->setFixedHeight(perHeight*1.5);
    mTypeComboBox->setFixedHeight(perHeight*1.5);
    mGainDial->setFixedHeight(perHeight*3);
    mFrequencyDial->setFixedHeight(perHeight*3);
    mQValueDial->setFixedHeight(perHeight*3);
    mEnabledCheckBox->setFixedHeight(perHeight);
}
