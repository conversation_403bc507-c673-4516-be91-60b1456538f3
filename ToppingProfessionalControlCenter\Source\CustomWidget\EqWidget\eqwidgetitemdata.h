#ifndef EQWIDGETITEMDATA_H
#define EQWIDGETITEMDATA_H

#include <QString>
#include <QVector>
#include <QPair>

struct EqWidgetItemData {
    QString type;
    float gain;
    float frequency;
    float qValue;
    bool enabled;

    uint8_t changeFlags;

    enum ChangeFlag : uint8_t {
        TYPE_CHANGED      = 0x01,
        GAIN_CHANGED      = 0x02,
        FREQUENCY_CHANGED = 0x04,
        QVALUE_CHANGED    = 0x08,
        ENABLED_CHANGED   = 0x10,
        ALL_CHANGED       = 0x1F
    };

    EqWidgetItemData()
        : type("high pass")
        , gain(0.0f)
        , frequency(1000.0f)
        , qValue(0.7f)
        , enabled(true)
        , changeFlags(0)
    {}

    void setChanged(ChangeFlag flag) { changeFlags |= flag; }
    void setChanged(uint8_t flags) { changeFlags |= flags; }

    void clearChanged(ChangeFlag flag) { changeFlags &= ~flag; }
    void clearAllChanged() { changeFlags = 0; }

    bool isChanged(ChangeFlag flag) const { return (changeFlags & flag) != 0; }
    bool hasAnyChanged() const { return changeFlags != 0; }
    uint8_t getChangeFlags() const { return changeFlags; }

    bool operator==(const EqWidgetItemData& other) const {
        return type == other.type &&
               gain == other.gain &&
               frequency == other.frequency &&
               qValue == other.qValue &&
               enabled == other.enabled;
    }

    bool isTypeChanged() const { return isChanged(TYPE_CHANGED); }
    bool isGainChanged() const { return isChanged(GAIN_CHANGED); }
    bool isFrequencyChanged() const { return isChanged(FREQUENCY_CHANGED); }
    bool isQValueChanged() const { return isChanged(QVALUE_CHANGED); }
    bool isEnabledChanged() const { return isChanged(ENABLED_CHANGED); }
    QVector<QPair<QString,QString>> getChangedAttributes() const {
        if(!hasAnyChanged()) return {};
        QVector<QPair<QString,QString>> changedAttributes;
        if (isTypeChanged()) changedAttributes.append({"type", type});
        if (isGainChanged()) changedAttributes.append({"gain", QString::number(gain)});
        if (isFrequencyChanged()) changedAttributes.append({"frequency", QString::number(frequency)});
        if (isQValueChanged()) changedAttributes.append({"qValue", QString::number(qValue)});
        if (isEnabledChanged()) changedAttributes.append({"enabled", QString::number(enabled)});
        return changedAttributes;
    }

    void updateType(const QString& newType) {
        if (type != newType) {
            type = newType;
            setChanged(TYPE_CHANGED);
        }
    }

    void updateGain(float newGain) {
        if (gain != newGain) {
            gain = newGain;
            setChanged(GAIN_CHANGED);
        }
    }

    void updateFrequency(float newFrequency) {
        if (frequency != newFrequency) {
            frequency = newFrequency;
            setChanged(FREQUENCY_CHANGED);
        }
    }

    void updateQValue(float newQValue) {
        if (qValue != newQValue) {
            qValue = newQValue;
            setChanged(QVALUE_CHANGED);
        }
    }

    void updateEnabled(bool newEnabled) {
        if (enabled != newEnabled) {
            enabled = newEnabled;
            setChanged(ENABLED_CHANGED);
        }
    }

    void updateData(const QString& newType, float newGain, float newFrequency,
                   float newQValue, bool newEnabled) {
        updateType(newType);
        updateGain(newGain);
        updateFrequency(newFrequency);
        updateQValue(newQValue);
        updateEnabled(newEnabled);
    }
};

#endif // EQWIDGETITEMDATA_H
