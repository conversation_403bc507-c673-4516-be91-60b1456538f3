#include "origins1m7.h"
#include "globalfont.h"
#include "ui_origins1m7.h"


OriginS1M7::OriginS1M7(QWidget* parent, QString name)
    : OriginBase(parent)
    , WorkspaceObserver(name)
    , AppSettingsObserver(name)
    , ui(new Ui::OriginS1M7)
{
    ui->setupUi(this);
    installEventFilter(this);
    resize(minimumWidth(), minimumHeight());
    QString style;
    style = "QFrame {"
            "   background-color: #161616;"
            "   border-radius: 8px;"
            "}";
    ui->frame->setStyleSheet(style);
    style = "QLineEdit {"
            "   color: rgb(161, 161, 161);"
            "   background-color: rgb(46, 46, 46);"
            "   border-radius: 5px;"
            "   selection-color: rgb(0, 121, 107);"
            "   selection-background-color: rgb(224, 247, 250);"
            "}";
    ui->lineEdit->setStyleSheet(style);
    ui->lineEdit->setAttribute(Qt::WA_Hover);
    ui->lineEdit->installEventFilter(this);
    style = "QPushButton {"
            "   background-color: transparent;"
            "   image: url(:/Icon/WidgetCloseBlack.png);"
            "}"
            "QPushButton:hover {"
            "   border: 2px solid rgb(46, 46, 46);"
            "   border-radius: 3px;"
            "}";
    ui->pushButtonClose->setStyleSheet(style);
    ui->pushButtonClose->setParent(this);
    ui->pushButtonClose->setFocusPolicy(Qt::NoFocus);
    ui->pushButtonClose->hide();
    ui->pushButtonClose->setAttribute(Qt::WA_Hover);
    ui->pushButtonClose->installEventFilter(this);
    ui->widgetLinkedVSlider->setRange(-90, 0).setDefault(0).showInfinitesimal(true);
    ui->widgetPushButtonGroup1->setPushButtonWeightWidth(50);
    mTimer.setSingleShot(true);
    mTimer.setInterval(100);
    connect(&mTimer, SIGNAL(timeout()), this, SLOT(in_mTimer_timeout()), Qt::UniqueConnection);
    connect(ui->widgetLinkedVSlider, SIGNAL(valueChanged(int)), this, SLOT(in_widgetLinkedVSlider_valueChanged(int)), Qt::UniqueConnection);
    connect(ui->widgetPushButtonGroup1, SIGNAL(buttonStateChanged(PushButtonS1M7::ButtonID, bool)), this, SLOT(in_widgetPushButtonGroup1_buttonStateChanged(PushButtonS1M7::ButtonID, bool)), Qt::UniqueConnection);
}
OriginS1M7::~OriginS1M7()
{
    delete ui;
}


// override
bool OriginS1M7::eventFilter(QObject* obj, QEvent* e)
{
    if((obj == ui->lineEdit || obj == ui->pushButtonClose) && isWidgetEnable() && isWidgetMovable())
    {
        if(e->type() == QEvent::HoverEnter)
        {
            if(ui->pushButtonClose->isHidden())
            {
                mTimer.start();
            }
            return true;
        }
        else if(e->type() == QEvent::HoverLeave)
        {
            QTimer::singleShot(0, [this](){
                if(!ui->lineEdit->underMouse() && !ui->pushButtonClose->underMouse())
                {
                    mTimer.stop();
                    ui->pushButtonClose->hide();
                }
            });
        }
        if(obj == ui->lineEdit && e->type() == QEvent::MouseButtonPress && ui->lineEdit->isEnabled())
        {
            mTimer.stop();
            ui->pushButtonClose->hide();
        }
    }
    else if(obj == this && isWidgetEnable() && isWidgetMovable())
    {
        if(e->type() == QEvent::Move)
        {
            QTimer::singleShot(0, [this](){
                if(ui->lineEdit->geometry().contains(ui->frame->mapFromGlobal(QCursor::pos())))
                {
                    mTimer.start();
                }
            });
        }
    }
    return QWidget::eventFilter(obj, e);
}
void OriginS1M7::resizeEvent(QResizeEvent* e)
{
    Q_UNUSED(e);
    // W
    float wPixelPerRatio=size().width() / 100.0;
    int wSpace4=wPixelPerRatio * 7;
    int wLinkedMeterLeft=wPixelPerRatio * 30;
    int wSpace5=wPixelPerRatio * 3;
    int wLinkedSlider=wPixelPerRatio * 24;
    int wSpace6=wPixelPerRatio * 2;
    int wLinkedMeterRight=wPixelPerRatio * 30;
    int xLinkedMeterLeft=wSpace4;
    int xLinkedSlider=xLinkedMeterLeft + wLinkedMeterLeft + wSpace5;
    int xLinkedMeterRight=xLinkedSlider + wLinkedSlider + wSpace6;
    // H
    float hPixelPerRatio = size().height() / 100.0;
    int hLineEdit=hPixelPerRatio * 8;
    int hSpace1=hPixelPerRatio * 0;
    int hToolButton=hPixelPerRatio * 0;
    int hSpace2=hPixelPerRatio * 5;
    int hSpace3=hPixelPerRatio * 3;
    int hMeter=hPixelPerRatio * 55;
    int hSpace4=hPixelPerRatio * 12;
    int hButtonGroup=hPixelPerRatio * 6;
    int hPushButtonClose=hLineEdit / 100.0 * 50;
    ui->lineEdit->setGeometry(0, 0, size().width(), hLineEdit);
    ui->pushButtonClose->setGeometry(size().width() - hPushButtonClose * 1.3, (hLineEdit - hPushButtonClose) / 2, hPushButtonClose, hPushButtonClose);
    ui->widgetLinkedMeterLeft->setGeometry(xLinkedMeterLeft, hLineEdit + hSpace1 + hToolButton + hSpace2 + hSpace3, wLinkedMeterLeft, hMeter);
    ui->widgetLinkedVSlider->setGeometry(xLinkedSlider, hLineEdit + hSpace1 + hToolButton + hSpace2, wLinkedSlider, hMeter + hSpace3);
    ui->widgetLinkedMeterRight->setGeometry(xLinkedMeterRight, hLineEdit + hSpace1 + hToolButton + hSpace2 + hSpace3, wLinkedMeterRight, hMeter);
    ui->widgetPushButtonGroup1->setGeometry(0, hLineEdit + hSpace1 + hToolButton + hSpace2 + hSpace3 + hMeter + hSpace4, size().width(), hButtonGroup);
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, ui->lineEdit->height()) - 3);
    if(mFont.pointSize() < 8)
    {
        mFont.setPointSize(mFont.pointSize());
    }
    else if(mFont.pointSize() < 12)
    {
        mFont.setPointSize(mFont.pointSize() - 1);
    }
    else if(mFont.pointSize() < 17)
    {
        mFont.setPointSize(mFont.pointSize() - 2);
    }
    else if(mFont.pointSize() < 22)
    {
        mFont.setPointSize(mFont.pointSize() - 3);
    }
    mFont.setPointSize(mFont.pointSize() - 1);
    ui->lineEdit->setFont(mFont);
    int radius=size().width() * 0.04;
    QString style;
    style = QString("QFrame {"
                    "   background-color: #161616;"
                    "   border-radius: %1px;"
                    "}").arg(radius);
    ui->frame->setStyleSheet(style);
    style = QString("QLineEdit {"
                    "   color: rgb(161, 161, 161);"
                    "   background-color: rgb(46, 46, 46);"
                    "   border-top-left-radius: %1px; border-top-right-radius: %1px;"
                    "   selection-color: rgb(0, 121, 107);"
                    "   selection-background-color: rgb(224, 247, 250);"
                    "}").arg(radius);
    ui->lineEdit->setStyleSheet(style);
}
void OriginS1M7::updateAttribute()
{
    if(isWidgetReady())
    {
        float gain;
        if(isWidgetEnable())
        {
            if(mMuteAffectGain)
            {
                gain = ui->widgetPushButtonGroup1->getPushButtonStateMUTE() ? (mDisableGAIN) : (ui->widgetLinkedVSlider->getValue());
            }
            else
            {
                gain = ui->widgetLinkedVSlider->getValue();
            }
            if(mPreGAIN != gain)
            {
                mPreGAIN = gain;
                emit attributeChanged(this->objectName(), "GAIN", QString::number(mPreGAIN));
            }
            if(mPreMUTE != static_cast<int>(ui->widgetPushButtonGroup1->getPushButtonStateMUTE()))
            {
                mPreMUTE = static_cast<int>(ui->widgetPushButtonGroup1->getPushButtonStateMUTE());
                emit attributeChanged(this->objectName(), "MUTE", QString::number(mPreMUTE));
            }
        }
        else
        {
            if(mMuteAffectGain)
            {
                gain = mDisableGAIN;
            }
            else
            {
                gain = ui->widgetLinkedVSlider->getValue();
            }
            if(mPreGAIN != gain)
            {
                mPreGAIN = gain;
                emit attributeChanged(this->objectName(), "GAIN", QString::number(mPreGAIN));
            }
            if(mPreMUTE != static_cast<int>(true))
            {
                mPreMUTE = static_cast<int>(true);
                emit attributeChanged(this->objectName(), "MUTE", QString::number(mPreMUTE));
            }
        }
    }
}
void OriginS1M7::loadSettings()
{
    mPreMUTE = -2147483648;
    mPreGAIN = -2147483648;
    setWidgetReady(false);
    WorkspaceObserver::getSettings()->beginGroup(objectName());
    bool flag=WorkspaceObserver::getSettings()->contains(objectName());
    WorkspaceObserver::getSettings()->endGroup();
    if(!flag)
    {
        WorkspaceObserver::setValue(objectName(), true);
        WorkspaceObserver::setValue("ChannelName", getChannelName());
        WorkspaceObserver::setValue("GAIN", ui->widgetLinkedVSlider->getDefault());
        WorkspaceObserver::setValue("MUTE", false);
    }
    ui->lineEdit->setText(WorkspaceObserver::value("ChannelName").toString());
    ui->widgetLinkedVSlider->setValue(WorkspaceObserver::value("GAIN").toInt());
    ui->widgetPushButtonGroup1->setPushButtonStateMUTE(WorkspaceObserver::value("MUTE").toBool());
    if(isWidgetEmitAction())
    {
        emit attributeChanged(this->objectName(), "Save_ChannelName", WorkspaceObserver::value("ChannelName").toString());
        emit attributeChanged(this->objectName(), "Save_Enable", QString::number(isWidgetEnable()));
        emit attributeChanged(this->objectName(), "Save_GAIN", WorkspaceObserver::value("GAIN").toString());
        emit attributeChanged(this->objectName(), "Save_MUTE", QString::number(WorkspaceObserver::value("MUTE").toBool()));
    }
    setWidgetReady(true);
    updateAttribute();
}
void OriginS1M7::AppSettingsChanged(QString objectName, QString attribute, QString value)
{
    Q_UNUSED(objectName);
    Q_UNUSED(attribute);
    Q_UNUSED(value);
}


// slot
void OriginS1M7::in_mTimer_timeout()
{
    ui->pushButtonClose->show();
}
void OriginS1M7::in_widgetLinkedVSlider_valueChanged(int value)
{
    save("GAIN", value);
    updateAttribute();
    if(mGainAffectMute && ui->widgetPushButtonGroup1->getPushButtonStateMUTE())
    {
        ui->widgetPushButtonGroup1->setPushButtonClickedMUTE(false);
    }
}
void OriginS1M7::in_widgetPushButtonGroup1_buttonStateChanged(PushButtonS1M7::ButtonID button, bool state)
{
    switch(button)
    {
        case PushButtonS1M7::buttonMUTE:
            save("MUTE", state);
            updateAttribute();
            break;
        default:
            break;
    }
}
void OriginS1M7::on_lineEdit_textChanged(const QString& arg1)
{
    QFont font=ui->lineEdit->font();
    font.setPointSize(5);
    if(!GLBFHandle.isSuitable(font, arg1, minimumWidth() - 6))
    {
        ui->lineEdit->setText(arg1.chopped(1));
    }
}
void OriginS1M7::on_lineEdit_editingFinished()
{
    if(ui->lineEdit->text().isEmpty())
    {
        ui->lineEdit->setText(getChannelName());
    }
    ui->lineEdit->clearFocus();
    save("ChannelName", ui->lineEdit->text());
}
void OriginS1M7::on_pushButtonClose_clicked()
{
    emit attributeChanged(getChannelName(), "Hide", QString::number(static_cast<int>(true)));
    ui->pushButtonClose->hide();
}


// setter & getter
void OriginS1M7::save(QAnyStringView key, const QVariant& value)
{
    WorkspaceObserver::setValue(key, value);
    if(isWidgetEmitAction())
    {
        emit attributeChanged(this->objectName(), "Save_" + key.toString(), value.typeId() == QMetaType::Bool ? (QString::number(value.toBool())) : (value.toString()));
    }
}
OriginS1M7& OriginS1M7::setName(QString name)
{
    setObjectName(name);
    WorkspaceObserver::setObserverName(name);
    AppSettingsObserver::setObserverName(name);
    return *this;
}
OriginS1M7& OriginS1M7::setFont(QFont font)
{
    mFont = font;
    ui->widgetLinkedMeterLeft->setFont(font);
    ui->widgetLinkedVSlider->setFont(font);
    ui->widgetLinkedMeterRight->setFont(font);
    ui->widgetPushButtonGroup1->setFont(font);
    QResizeEvent e(size(), size());
    resizeEvent(&e);
    update();
    return *this;
}
OriginS1M7& OriginS1M7::setVolumeMeterLeft(int value)
{
    ui->widgetLinkedMeterLeft->setValue(value, ui->widgetPushButtonGroup1->getPushButtonStateMUTE() ? (-90) : (ui->widgetLinkedVSlider->getValue()));
    return *this;
}
OriginS1M7& OriginS1M7::setVolumeMeterLeftClear()
{
    ui->widgetLinkedMeterLeft->setMeterClear();
    return *this;
}
OriginS1M7& OriginS1M7::setVolumeMeterLeftSlip()
{
    ui->widgetLinkedMeterLeft->setMeterSlip();
    return *this;
}
OriginS1M7& OriginS1M7::setVolumeMeterRight(int value)
{
    ui->widgetLinkedMeterRight->setValue(value, ui->widgetPushButtonGroup1->getPushButtonStateMUTE() ? (-90) : (ui->widgetLinkedVSlider->getValue()));
    return *this;
}
OriginS1M7& OriginS1M7::setVolumeMeterRightClear()
{
    ui->widgetLinkedMeterRight->setMeterClear();
    return *this;
}
OriginS1M7& OriginS1M7::setVolumeMeterRightSlip()
{
    ui->widgetLinkedMeterRight->setMeterSlip();
    return *this;
}
OriginS1M7& OriginS1M7::setGain(float value)
{
    ui->widgetLinkedVSlider->setValue(value);
    return *this;
}
OriginS1M7& OriginS1M7::setGainLock(bool state)
{
    ui->widgetLinkedVSlider->setEnabled(!state);
    return *this;
}
OriginS1M7& OriginS1M7::setMuteAffectGain(bool state)
{
    mMuteAffectGain = state;
    return *this;
}
OriginS1M7& OriginS1M7::setGainAffectMute(bool state)
{
    mGainAffectMute = state;
    return *this;
}
OriginS1M7& OriginS1M7::setGainRange(float min, float max)
{
    ui->widgetLinkedVSlider->setRange(min, max);
    return *this;
}
OriginS1M7& OriginS1M7::setGainDefault(float value)
{
    ui->widgetLinkedVSlider->setDefault(value);
    return *this;
}
OriginS1M7& OriginS1M7::setGainWidgetDisable(float value)
{
    mDisableGAIN = value;
    return *this;
}
OriginS1M7& OriginS1M7::setChannelNameEditable(bool state)
{
    ui->lineEdit->setEnabled(state);
    return *this;
}

