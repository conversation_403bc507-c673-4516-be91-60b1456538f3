#include "globalfont.h"
#include "outputs1m2.h"
#include "ui_outputs1m2.h"


OutputS1M2::OutputS1M2(QWidget* parent, QString name)
    : OutputBase(parent)
    , WorkspaceObserver(name)
    , AppSettingsObserver(name)
    , ui(new Ui::OutputS1M2)
{
    ui->setupUi(this);
    installEventFilter(this);
    resize(minimumWidth(), minimumHeight());
    QString style;
    style = "QFrame {"
            "   background-color: #161616;"
            "   border-radius: 8px;"
            "}";
    ui->frame->setStyleSheet(style);
    style = "QWidget {"
            "   background-color: rgba(31, 31, 31, 153);"
            "   border-radius: 8px;"
            "}";
    ui->widgetOverlay->setStyleSheet(style);
    ui->widgetOverlay->setAttribute(Qt::WA_TransparentForMouseEvents);
    ui->widgetOverlay->setHidden(true);
    style = "QLineEdit {"
            "   color: rgb(161, 161, 161);"
            "   background-color: rgb(46, 46, 46);"
            "   border-radius: 8px;"
            "   selection-color: rgb(0, 121, 107);"
            "   selection-background-color: rgb(224, 247, 250);"
            "}";
    ui->lineEdit->setStyleSheet(style);
    ui->lineEdit->setAttribute(Qt::WA_Hover);
    ui->lineEdit->installEventFilter(this);
    style = "QPushButton {"
            "   border-radius: 3px;"
            "   color: rgb(161, 161, 161);"
            "   background-color: transparent;"
            "}"
            "QPushButton:hover {"
            "   color: rgb(67, 207, 124);"
            "}";
    ui->pushButtonLink->setStyleSheet(style);
    style = "QPushButton {"
            "   background-color: transparent;"
            "   image: url(:/Icon/WidgetCloseBlack.png);"
            "}"
            "QPushButton:hover {"
            "   border: 2px solid rgb(46, 46, 46);"
            "   border-radius: 3px;"
            "}";
    ui->pushButtonClose->setStyleSheet(style);
    ui->pushButtonClose->setParent(this);
    ui->pushButtonClose->setFocusPolicy(Qt::NoFocus);
    ui->pushButtonClose->hide();
    ui->pushButtonClose->setAttribute(Qt::WA_Hover);
    ui->pushButtonClose->installEventFilter(this);
    ui->widgetLinkedMeterLeft->setWidthRatio(44, 6, 20, 16, 14);
    ui->widgetLinkedMeterRight->setWidthRatio(44, 6, 20, 16, 14);
    ui->widgetUnlinkMeter->setWidthRatio(9, 6, 12, 9, 28);
    ui->widgetLinkedMeterLeft->setHeightRatio(2, 2, 94, 2);
    ui->widgetLinkedMeterRight->setHeightRatio(2, 2, 94, 2);
    ui->widgetUnlinkMeter->setHeightRatio(2, 2, 94, 2);
    ui->widgetLinkedMeterLeft->setScaleLineHidden(true);
    ui->widgetLinkedMeterRight->setScaleLineHidden(true);
    ui->widgetUnlinkMeter->setScaleLineHidden(true);
    ui->widgetLinkedVSlider->setRange(0, -10, -20, -30).setDefault(0).setHeightRatio(6, 4, 90).showInfinitesimal(true);
    ui->widgetUnlinkVSliderLeft->setRange(0, -10, -20, -30).setDefault(0).setHeightRatio(6, 4, 90).showInfinitesimal(true);
    ui->widgetUnlinkVSliderRight->setRange(0, -10, -20, -30).setDefault(0).setHeightRatio(6, 4, 90).showInfinitesimal(true);
    ui->widgetLinkedMeterLeft->setHidden(true);
    ui->widgetLinkedVSlider->setHidden(true);
    ui->widgetLinkedMeterRight->setHidden(true);
    ui->widgetPushButtonGroup1->setHidden(true);
    ui->widgetUnlinkVSliderLeft->setHidden(true);
    ui->widgetUnlinkMeter->setHidden(true);
    ui->widgetUnlinkVSliderRight->setHidden(true);
    ui->widgetPushButtonGroup2->setHidden(true);
    ui->widgetPushButtonGroup3->setHidden(true);
    mTimer.setSingleShot(true);
    mTimer.setInterval(100);
    connect(&mTimer, SIGNAL(timeout()), this, SLOT(in_mTimer_timeout()), Qt::UniqueConnection);
    connect(ui->widgetLinkedVSlider, SIGNAL(valueChanged(float)), this, SLOT(in_widgetLinkedVSlider_valueChanged(float)), Qt::UniqueConnection);
    connect(ui->widgetUnlinkVSliderLeft, SIGNAL(valueChanged(float)), this, SLOT(in_widgetUnlinkVSliderLeft_valueChanged(float)), Qt::UniqueConnection);
    connect(ui->widgetUnlinkVSliderRight, SIGNAL(valueChanged(float)), this, SLOT(in_widgetUnlinkVSliderRight_valueChanged(float)), Qt::UniqueConnection);
    connect(ui->widgetToolButton, SIGNAL(actionChanged(QString)), this, SLOT(in_widgetToolButton_actionChanged(QString)), Qt::UniqueConnection);
    connect(ui->widgetPushButtonGroup1, SIGNAL(stateChanged(QString, QString)), this, SLOT(in_widgetPushButtonGroup1_stateChanged(QString, QString)), Qt::UniqueConnection);
    connect(ui->widgetPushButtonGroup2, SIGNAL(stateChanged(QString, QString)), this, SLOT(in_widgetPushButtonGroup2_stateChanged(QString, QString)), Qt::UniqueConnection);
    connect(ui->widgetPushButtonGroup3, SIGNAL(stateChanged(QString, QString)), this, SLOT(in_widgetPushButtonGroup3_stateChanged(QString, QString)), Qt::UniqueConnection);
}
OutputS1M2::~OutputS1M2()
{
    delete ui;
}


// override
bool OutputS1M2::eventFilter(QObject* obj, QEvent* e)
{
    if((obj == ui->lineEdit || obj == ui->pushButtonClose) && isWidgetEnable() && isWidgetMovable())
    {
        if(e->type() == QEvent::HoverEnter)
        {
            if(ui->pushButtonClose->isHidden())
            {
                mTimer.start();
            }
            return true;
        }
        else if(e->type() == QEvent::HoverLeave)
        {
            QTimer::singleShot(0, [this](){
                if(!ui->lineEdit->underMouse() && !ui->pushButtonClose->underMouse())
                {
                    mTimer.stop();
                    ui->pushButtonClose->hide();
                }
            });
        }
        if(obj == ui->lineEdit && e->type() == QEvent::MouseButtonPress && ui->lineEdit->isEnabled())
        {
            mTimer.stop();
            ui->pushButtonClose->hide();
        }
    }
    else if(obj == this && isWidgetEnable() && isWidgetMovable())
    {
        if(e->type() == QEvent::Move)
        {
            QTimer::singleShot(0, [this](){
                if(ui->lineEdit->geometry().contains(ui->frame->mapFromGlobal(QCursor::pos())))
                {
                    mTimer.start();
                }
            });
        }
    }
    return QWidget::eventFilter(obj, e);
}
void OutputS1M2::resizeEvent(QResizeEvent* e)
{
    Q_UNUSED(e);
    // W
    float wPixelPerRatio=size().width() / 100.0;
    int wSpace1=wPixelPerRatio * 10.5;
    int wUnlinkSliderLeft=wPixelPerRatio * 18;
    int wSpace2=wPixelPerRatio * 0;
    int wUnlinkMeter=wPixelPerRatio * 42;
    int wSpace3=wPixelPerRatio * 0;
    int wUnlinkSliderRight=wPixelPerRatio * 18;
    int xUnlinkSliderLeft=wSpace1 + (size().width() - wSpace1 - wUnlinkSliderLeft - wSpace2 - wUnlinkMeter - wSpace3 - wUnlinkSliderRight - wSpace1) / 2;
    int xUnlinkMeter=xUnlinkSliderLeft + wUnlinkSliderLeft + wSpace2;
    int xUnlinkSliderRight=xUnlinkMeter + wUnlinkMeter + wSpace3;
    int wSpace4=wPixelPerRatio * 14;
    int wLinkedMeterLeft=wPixelPerRatio * 25;
    int wSpace5=wPixelPerRatio * 2;
    int wLinkedSlider=wPixelPerRatio * 18;
    int wSpace6=wPixelPerRatio * 2;
    int wLinkedMeterRight=wPixelPerRatio * 25;
    int xLinkedMeterLeft=wSpace4 + (size().width() - wSpace4 - wLinkedMeterLeft - wSpace5 - wLinkedSlider - wSpace6 - wLinkedMeterRight - wSpace4) / 2;
    int xLinkedSlider=xLinkedMeterLeft + wLinkedMeterLeft + wSpace5;
    int xLinkedMeterRight=xLinkedSlider + wLinkedSlider + wSpace6;
    // H
    float hPixelPerRatio = size().height() / 100.0;
    int hLineEdit=hPixelPerRatio * 8;
    int hSpace1=hPixelPerRatio * 2;
    int hToolButton=hPixelPerRatio * 8;
    int hSpace2=hPixelPerRatio * 0;
    int hPushButtonLink=hPixelPerRatio * 3.1;
    int hSpace3=hPixelPerRatio * 11;
    int hMeter=hPixelPerRatio * 62;
    int hSpace4=hPixelPerRatio * 0;
    int hButtonGroup=hPixelPerRatio * 5;
    int hPushButtonClose=hLineEdit / 100.0 * 50;
    ui->lineEdit->setGeometry(0, 0, size().width(), hLineEdit);
    ui->pushButtonClose->setGeometry(size().width() - hPushButtonClose * 1.3, (hLineEdit - hPushButtonClose) / 2, hPushButtonClose, hPushButtonClose);
    ui->widgetToolButton->setGeometry(wPixelPerRatio * 12, hLineEdit + hSpace1, size().width() - wPixelPerRatio * 24, hToolButton);
    ui->pushButtonLink->setGeometry(size().width() / 2 - hPushButtonLink * 1.2, hLineEdit + hSpace1 + hToolButton + hSpace2, hPushButtonLink * 2.4, hPushButtonLink * 1.4);
    ui->widgetUnlinkVSliderLeft->setGeometry(xUnlinkSliderLeft, hLineEdit + hSpace1 + hPushButtonLink + hSpace2 + hPushButtonLink + hSpace3 - hPixelPerRatio * 4.0, wUnlinkSliderLeft, hMeter + hPixelPerRatio * 4.0);
    ui->widgetUnlinkMeter->setGeometry(xUnlinkMeter, hLineEdit + hSpace1 + hPushButtonLink + hSpace2 + hPushButtonLink + hSpace3, wUnlinkMeter, hMeter);
    ui->widgetUnlinkVSliderRight->setGeometry(xUnlinkSliderRight, hLineEdit + hSpace1 + hPushButtonLink + hSpace2 + hPushButtonLink + hSpace3 - hPixelPerRatio * 4.0, wUnlinkSliderRight, hMeter + hPixelPerRatio * 4.0);
    ui->widgetLinkedMeterLeft->setGeometry(xLinkedMeterLeft, hLineEdit + hSpace1 + hPushButtonLink + hSpace2 + hPushButtonLink + hSpace3, wLinkedMeterLeft, hMeter);
    ui->widgetLinkedVSlider->setGeometry(xLinkedSlider, hLineEdit + hSpace1 + hPushButtonLink + hSpace2 + hPushButtonLink + hSpace3 - hPixelPerRatio * 4.0, wLinkedSlider, hMeter + hPixelPerRatio * 4.0);
    ui->widgetLinkedMeterRight->setGeometry(xLinkedMeterRight, hLineEdit + hSpace1 + hPushButtonLink + hSpace2 + hPushButtonLink + hSpace3, wLinkedMeterRight, hMeter);
    ui->widgetPushButtonGroup1->setHidden(false);
    ui->widgetPushButtonGroup2->setHidden(false);
    ui->widgetPushButtonGroup3->setHidden(false);
    ui->widgetPushButtonGroup1->setGeometry(wPixelPerRatio * 20.1, hPixelPerRatio * 88, size().width() - wPixelPerRatio * 40.2, (size().width() - wPixelPerRatio * 40.2) / ui->widgetPushButtonGroup1->minimumWidth() * ui->widgetPushButtonGroup1->minimumHeight());
    ui->widgetPushButtonGroup2->setGeometry(wPixelPerRatio * 10.5, hPixelPerRatio * 88, size().width() - wPixelPerRatio * 60, (size().width() - wPixelPerRatio * 60) / ui->widgetPushButtonGroup2->minimumWidth() * ui->widgetPushButtonGroup2->minimumHeight());
    ui->widgetPushButtonGroup3->setGeometry(wPixelPerRatio * 10.5 + ui->widgetPushButtonGroup2->width(), hPixelPerRatio * 88, size().width() - wPixelPerRatio * 60, (size().width() - wPixelPerRatio * 60) / ui->widgetPushButtonGroup3->minimumWidth() * ui->widgetPushButtonGroup3->minimumHeight());
    if(mLinkState)
    {
        ui->widgetPushButtonGroup2->setHidden(true);
        ui->widgetPushButtonGroup3->setHidden(true);
    }
    else
    {
        ui->widgetPushButtonGroup1->setHidden(true);
    }
    ui->widgetOverlay->setGeometry(rect().x(), rect().y(), rect().width(), hLineEdit);
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, ui->lineEdit->height()) - 3);
    if(mFont.pointSize() < 8)
    {
        mFont.setPointSize(mFont.pointSize());
    }
    else if(mFont.pointSize() < 12)
    {
        mFont.setPointSize(mFont.pointSize() - 1);
    }
    else if(mFont.pointSize() < 17)
    {
        mFont.setPointSize(mFont.pointSize() - 2);
    }
    else if(mFont.pointSize() < 22)
    {
        mFont.setPointSize(mFont.pointSize() - 3);
    }
    mFont.setPointSize(mFont.pointSize() - 1);
    ui->lineEdit->setFont(mFont);
    int radius=size().width() * 0.04;
    QString style;
    style = QString("QFrame {"
                    "   background-color: #161616;"
                    "   border-radius: %1px;"
                    "}").arg(radius);
    ui->frame->setStyleSheet(style);
    style = QString("QWidget {"
                    "   background-color: rgba(31, 31, 31, 153);"
                    "   border-top-left-radius: %1px; border-top-right-radius: %1px;"
                    "}").arg(radius);
    ui->widgetOverlay->setStyleSheet(style);
    style = QString("QLineEdit {"
                    "   color: rgb(161, 161, 161);"
                    "   background-color: rgb(46, 46, 46);"
                    "   border-top-left-radius: %1px; border-top-right-radius: %1px;"
                    "   selection-color: rgb(0, 121, 107);"
                    "   selection-background-color: rgb(224, 247, 250);"
                    "}").arg(radius);
    ui->lineEdit->setStyleSheet(style);
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, ui->pushButtonLink->height()));
    ui->pushButtonLink->setFont(mFont);
}
void OutputS1M2::updateAttribute()
{
    if(isWidgetReady())
    {
        float gainLeft, gainRight;
        if(isWidgetEnable())
        {
            if(mMuteAffectGain)
            {
                if(mLinkState)
                {
                    gainLeft = ui->widgetPushButtonGroup1->getState("MUTE").toInt() ? (mDisableGAIN) : (ui->widgetLinkedVSlider->getValue());
                    gainRight = ui->widgetPushButtonGroup1->getState("MUTE").toInt() ? (mDisableGAIN) : (ui->widgetLinkedVSlider->getValue());
                }
                else
                {
                    gainLeft = ui->widgetPushButtonGroup2->getState("MUTE").toInt() ? (mDisableGAIN) : (ui->widgetUnlinkVSliderLeft->getValue());
                    gainRight = ui->widgetPushButtonGroup3->getState("MUTE").toInt() ? (mDisableGAIN) : (ui->widgetUnlinkVSliderRight->getValue());
                }
            }
            else
            {
                if(mLinkState)
                {
                    gainLeft = ui->widgetLinkedVSlider->getValue();
                    gainRight = ui->widgetLinkedVSlider->getValue();
                }
                else
                {
                    gainLeft = ui->widgetUnlinkVSliderLeft->getValue();
                    gainRight = ui->widgetUnlinkVSliderRight->getValue();
                }
            }
            if(mPreAudioSource != WorkspaceObserver::value("AudioSource").toString())
            {
                mPreAudioSource = WorkspaceObserver::value("AudioSource").toString();
                emit attributeChanged(this->objectName(), "AudioSource", mPreAudioSource);
            }
            if(mPreGAINLeft != gainLeft)
            {
                mPreGAINLeft = gainLeft;
                emit attributeChanged(this->objectName(), "GAINLeft", QString::number(mPreGAINLeft));
            }
            if(mPreGAINRight != gainRight)
            {
                mPreGAINRight = gainRight;
                emit attributeChanged(this->objectName(), "GAINRight", QString::number(mPreGAINRight));
            }
            if(mPreMUTE != ui->widgetPushButtonGroup1->getState("MUTE").toInt())
            {
                mPreMUTE = ui->widgetPushButtonGroup1->getState("MUTE").toInt();
                emit attributeChanged(this->objectName(), "MUTE", QString::number(mPreMUTE));
            }
            if(mPreMUTELeft != ui->widgetPushButtonGroup2->getState("MUTE").toInt())
            {
                mPreMUTELeft = ui->widgetPushButtonGroup2->getState("MUTE").toInt();
                emit attributeChanged(this->objectName(), "MUTELeft", QString::number(mPreMUTELeft));
            }
            if(mPreMUTERight != ui->widgetPushButtonGroup3->getState("MUTE").toInt())
            {
                mPreMUTERight = ui->widgetPushButtonGroup3->getState("MUTE").toInt();
                emit attributeChanged(this->objectName(), "MUTERight", QString::number(mPreMUTERight));
            }
        }
        else
        {
            if(mMuteAffectGain)
            {
                gainLeft = mDisableGAIN;
                gainRight = mDisableGAIN;
            }
            else
            {
                if(mLinkState)
                {
                    gainLeft = ui->widgetLinkedVSlider->getValue();
                    gainRight = ui->widgetLinkedVSlider->getValue();
                }
                else
                {
                    gainLeft = ui->widgetUnlinkVSliderLeft->getValue();
                    gainRight = ui->widgetUnlinkVSliderRight->getValue();
                }
            }
            if(mPreGAINLeft != gainLeft)
            {
                mPreGAINLeft = gainLeft;
                emit attributeChanged(this->objectName(), "GAINLeft", QString::number(mPreGAINLeft));
            }
            if(mPreGAINRight != gainRight)
            {
                mPreGAINRight = gainRight;
                emit attributeChanged(this->objectName(), "GAINRight", QString::number(mPreGAINRight));
            }
            if(mPreMUTE != static_cast<int>(true))
            {
                mPreMUTE = static_cast<int>(true);
                emit attributeChanged(this->objectName(), "MUTE", QString::number(mPreMUTE));
            }
            if(mPreMUTELeft != static_cast<int>(true))
            {
                mPreMUTELeft = static_cast<int>(true);
                emit attributeChanged(this->objectName(), "MUTELeft", QString::number(mPreMUTELeft));
            }
            if(mPreMUTERight != static_cast<int>(true))
            {
                mPreMUTERight = static_cast<int>(true);
                emit attributeChanged(this->objectName(), "MUTERight", QString::number(mPreMUTERight));
            }
        }
    }
}
void OutputS1M2::loadSettings()
{
    mPreAudioSource = "";
    mPreMUTE = -2147483648;
    mPreGAINLeft = -2147483648;
    mPreGAINRight = -2147483648;
    setWidgetReady(false);
    WorkspaceObserver::getSettings()->beginGroup(objectName());
    bool flag=WorkspaceObserver::getSettings()->contains(objectName());
    WorkspaceObserver::getSettings()->endGroup();
    if(!flag)
    {
        WorkspaceObserver::setValue(objectName(), true);
        WorkspaceObserver::setValue("ChannelName", getChannelName());
        WorkspaceObserver::setValue("AudioSource", mAudioSourceDefault);
        WorkspaceObserver::setValue("Link", mLinkDefaultState);
        WorkspaceObserver::setValue("GAIN", ui->widgetLinkedVSlider->getDefault());
        WorkspaceObserver::setValue("GAINLeft", ui->widgetLinkedVSlider->getDefault());
        WorkspaceObserver::setValue("GAINRight", ui->widgetLinkedVSlider->getDefault());
        WorkspaceObserver::setValue("MUTE", false);
        WorkspaceObserver::setValue("MUTELeft", false);
        WorkspaceObserver::setValue("MUTERight", false);
    }
    ui->lineEdit->setText(WorkspaceObserver::value("ChannelName").toString());
    QString style;
    if(WorkspaceObserver::value("Link").toBool())
    {
        ui->widgetLinkedVSlider->setHidden(false);
        ui->widgetLinkedMeterLeft->setHidden(false);
        ui->widgetLinkedMeterRight->setHidden(false);
        ui->widgetPushButtonGroup1->setHidden(false);
        ui->widgetUnlinkVSliderLeft->setHidden(true);
        ui->widgetUnlinkVSliderRight->setHidden(true);
        ui->widgetUnlinkMeter->setHidden(true);
        ui->widgetPushButtonGroup2->setHidden(true);
        ui->widgetPushButtonGroup3->setHidden(true);
        style = "QPushButton {"
                "   border-radius: 3px;"
                "   color: rgb(161, 161, 161);"
                "   background-color: transparent;"
                "}"
                "QPushButton:hover {"
                "   color: rgb(67, 207, 124);"
                "}";
        ui->pushButtonLink->setStyleSheet(style);
        mLinkState = true;
    }
    else
    {
        ui->widgetLinkedVSlider->setHidden(true);
        ui->widgetLinkedMeterLeft->setHidden(true);
        ui->widgetLinkedMeterRight->setHidden(true);
        ui->widgetPushButtonGroup1->setHidden(true);
        ui->widgetUnlinkVSliderLeft->setHidden(false);
        ui->widgetUnlinkVSliderRight->setHidden(false);
        ui->widgetUnlinkMeter->setHidden(false);
        ui->widgetPushButtonGroup2->setHidden(false);
        ui->widgetPushButtonGroup3->setHidden(false);
        style = "QPushButton {"
                "   border-radius: 3px;"
                "   color: rgb(60, 60, 60);"
                "   background-color: transparent;"
                "}"
                "QPushButton:hover {"
                "   color: rgb(67, 207, 124);"
                "}";
        ui->pushButtonLink->setStyleSheet(style);
        mLinkState = false;
    }
    ui->widgetToolButton->setActionTriggered(WorkspaceObserver::value("AudioSource").toString());
    ui->widgetLinkedVSlider->setValue(WorkspaceObserver::value("GAIN").toInt());
    ui->widgetUnlinkVSliderLeft->setValue(WorkspaceObserver::value("GAINLeft").toInt());
    ui->widgetUnlinkVSliderRight->setValue(WorkspaceObserver::value("GAINRight").toInt());
    ui->widgetPushButtonGroup1->setState("MUTE", QString::number(WorkspaceObserver::value("MUTE").toBool()), false);
    ui->widgetPushButtonGroup2->setState("MUTE", QString::number(WorkspaceObserver::value("MUTELeft").toBool()), false);
    ui->widgetPushButtonGroup3->setState("MUTE", QString::number(WorkspaceObserver::value("MUTERight").toBool()), false);
    if(isWidgetEmitAction())
    {
        emit attributeChanged(this->objectName(), "Save_ChannelName", WorkspaceObserver::value("ChannelName").toString());
        emit attributeChanged(this->objectName(), "Save_Enable", QString::number(isWidgetEnable()));
        emit attributeChanged(this->objectName(), "Save_AudioSource", WorkspaceObserver::value("AudioSource").toString());
        emit attributeChanged(this->objectName(), "Save_Link", QString::number(mLinkState));
        emit attributeChanged(this->objectName(), "Save_GAIN", WorkspaceObserver::value("GAIN").toString());
        emit attributeChanged(this->objectName(), "Save_GAINLeft", WorkspaceObserver::value("GAINLeft").toString());
        emit attributeChanged(this->objectName(), "Save_GAINRight", WorkspaceObserver::value("GAINRight").toString());
        emit attributeChanged(this->objectName(), "Save_MUTE", QString::number(WorkspaceObserver::value("MUTE").toBool()));
        emit attributeChanged(this->objectName(), "Save_MUTELeft", QString::number(WorkspaceObserver::value("MUTELeft").toBool()));
        emit attributeChanged(this->objectName(), "Save_MUTERight", QString::number(WorkspaceObserver::value("MUTERight").toBool()));
    }
    setWidgetReady(true);
    updateAttribute();
}
void OutputS1M2::AppSettingsChanged(QString objectName, QString attribute, QString value)
{
    Q_UNUSED(objectName);
    Q_UNUSED(attribute);
    Q_UNUSED(value);
}


// slot
void OutputS1M2::in_mTimer_timeout()
{
    ui->pushButtonClose->show();
}
void OutputS1M2::in_widgetLinkedVSlider_valueChanged(float value)
{
    save("GAIN", value);
    updateAttribute();
    if(mGainAffectMute && ui->widgetPushButtonGroup1->getState("MUTE").toInt())
    {
        ui->widgetPushButtonGroup1->setState("MUTE", QString::number(false), true);
    }
}
void OutputS1M2::in_widgetUnlinkVSliderLeft_valueChanged(float value)
{
    save("GAINLeft", value);
    updateAttribute();
}
void OutputS1M2::in_widgetUnlinkVSliderRight_valueChanged(float value)
{
    save("GAINRight", value);
    updateAttribute();
}
void OutputS1M2::in_widgetToolButton_actionChanged(QString actionName)
{
    save("AudioSource", actionName);
    updateAttribute();
    setVolumeMeterLeftSlip();
    setVolumeMeterRightSlip();
}
void OutputS1M2::in_widgetPushButtonGroup1_stateChanged(QString button, QString state)
{
    if(button == "MUTE")
    {
        save("MUTE", (bool) state.toInt());
        updateAttribute();
    }
}
void OutputS1M2::in_widgetPushButtonGroup2_stateChanged(QString button, QString state)
{
    if(button == "MUTE")
    {
        save("MUTELeft", (bool) state.toInt());
        updateAttribute();
    }
}
void OutputS1M2::in_widgetPushButtonGroup3_stateChanged(QString button, QString state)
{
    if(button == "MUTE")
    {
        save("MUTERight", (bool) state.toInt());
        updateAttribute();
    }
}
void OutputS1M2::on_lineEdit_textChanged(const QString& arg1)
{
    QFont font=ui->lineEdit->font();
    font.setPointSize(5);
    if(!GLBFHandle.isSuitable(font, arg1, minimumWidth() - 6))
    {
        ui->lineEdit->setText(arg1.chopped(1));
    }
}
void OutputS1M2::on_lineEdit_editingFinished()
{
    if(ui->lineEdit->text().isEmpty())
    {
        ui->lineEdit->setText(getChannelName());
    }
    ui->lineEdit->clearFocus();
    save("ChannelName", ui->lineEdit->text());
}
void OutputS1M2::on_pushButtonClose_clicked()
{
    emit attributeChanged(getChannelName(), "Hide", QString::number(static_cast<int>(true)));
    ui->pushButtonClose->hide();
}
void OutputS1M2::on_pushButtonLink_clicked()
{
    QString style;
    if(mLinkState)
    {
        ui->widgetLinkedVSlider->setHidden(true);
        ui->widgetLinkedMeterLeft->setHidden(true);
        ui->widgetLinkedMeterRight->setHidden(true);
        ui->widgetPushButtonGroup1->setHidden(true);
        ui->widgetUnlinkVSliderLeft->setHidden(false);
        ui->widgetUnlinkVSliderRight->setHidden(false);
        ui->widgetUnlinkMeter->setHidden(false);
        ui->widgetPushButtonGroup2->setHidden(false);
        ui->widgetPushButtonGroup3->setHidden(false);
        style = "QPushButton {"
                "   border-radius: 3px;"
                "   color: rgb(60, 60, 60);"
                "   background-color: transparent;"
                "}"
                "QPushButton:hover {"
                "   color: rgb(67, 207, 124);"
                "}";
        ui->pushButtonLink->setStyleSheet(style);
        mLinkState = false;
    }
    else
    {
        ui->widgetLinkedVSlider->setHidden(false);
        ui->widgetLinkedMeterLeft->setHidden(false);
        ui->widgetLinkedMeterRight->setHidden(false);
        ui->widgetPushButtonGroup1->setHidden(false);
        ui->widgetUnlinkVSliderLeft->setHidden(true);
        ui->widgetUnlinkVSliderRight->setHidden(true);
        ui->widgetUnlinkMeter->setHidden(true);
        ui->widgetPushButtonGroup2->setHidden(true);
        ui->widgetPushButtonGroup3->setHidden(true);
        style = "QPushButton {"
                "   border-radius: 3px;"
                "   color: rgb(161, 161, 161);"
                "   background-color: transparent;"
                "}"
                "QPushButton:hover {"
                "   color: rgb(67, 207, 124);"
                "}";
        ui->pushButtonLink->setStyleSheet(style);
        mLinkState = true;
    }
    save("Link", mLinkState);
    updateAttribute();
}


// setter & getter
void OutputS1M2::save(QAnyStringView key, const QVariant& value)
{
    WorkspaceObserver::setValue(key, value);
    if(isWidgetEmitAction())
    {
        emit attributeChanged(this->objectName(), "Save_" + key.toString(), value.typeId() == QMetaType::Bool ? (QString::number(value.toBool())) : (value.toString()));
    }
}
OutputS1M2& OutputS1M2::setName(QString name)
{
    setObjectName(name);
    WorkspaceObserver::setObserverName(name);
    AppSettingsObserver::setObserverName(name);
    return *this;
}
OutputS1M2& OutputS1M2::setFont(QFont font)
{
    mFont = font;
    ui->widgetToolButton->setFont(mFont);
    ui->widgetUnlinkVSliderLeft->setFont(font);
    ui->widgetUnlinkMeter->setFont(font);
    ui->widgetUnlinkVSliderRight->setFont(font);
    ui->widgetLinkedMeterLeft->setFont(font);
    ui->widgetLinkedVSlider->setFont(font);
    ui->widgetLinkedMeterRight->setFont(font);
    ui->widgetPushButtonGroup1->setFont(font);
    ui->widgetPushButtonGroup2->setFont(font);
    ui->widgetPushButtonGroup3->setFont(font);
    QResizeEvent e(size(), size());
    resizeEvent(&e);
    update();
    return *this;
}
OutputS1M2& OutputS1M2::setVolumeMeterLeft(int value)
{
    value = qMax(-900, value);
    int gain=0;
    if(mLinkState)
    {
        gain = ui->widgetPushButtonGroup1->getState("MUTE").toInt() ? (-90) : (ui->widgetLinkedVSlider->getValue());
    }
    else
    {
        gain = ui->widgetPushButtonGroup2->getState("MUTE").toInt() ? (-90) : (ui->widgetUnlinkVSliderLeft->getValue());
    }
    ui->widgetLinkedMeterLeft->setValue(value, gain);
    ui->widgetUnlinkMeter->setValueLeft(value, gain);
    return *this;
}
OutputS1M2& OutputS1M2::setVolumeMeterLeftClear()
{
    ui->widgetLinkedMeterLeft->setMeterClear();
    ui->widgetUnlinkMeter->setMeterLeftClear();
    return *this;
}
OutputS1M2& OutputS1M2::setVolumeMeterLeftSlip()
{
    ui->widgetLinkedMeterLeft->setMeterSlip();
    ui->widgetUnlinkMeter->setMeterLeftSlip();
    return *this;
}
OutputS1M2& OutputS1M2::setVolumeMeterRight(int value)
{
    value = qMax(-900, value);
    int gain=0;
    if(mLinkState)
    {
        gain = ui->widgetPushButtonGroup1->getState("MUTE").toInt() ? (-90) : (ui->widgetLinkedVSlider->getValue());
    }
    else
    {
        gain = ui->widgetPushButtonGroup3->getState("MUTE").toInt() ? (-90) : (ui->widgetUnlinkVSliderRight->getValue());
    }
    ui->widgetLinkedMeterRight->setValue(value, gain);
    ui->widgetUnlinkMeter->setValueRight(value, gain);
    return *this;
}
OutputS1M2& OutputS1M2::setVolumeMeterRightClear()
{
    ui->widgetLinkedMeterRight->setMeterClear();
    ui->widgetUnlinkMeter->setMeterRightClear();
    return *this;
}
OutputS1M2& OutputS1M2::setVolumeMeterRightSlip()
{
    ui->widgetLinkedMeterRight->setMeterSlip();
    ui->widgetUnlinkMeter->setMeterRightSlip();
    return *this;
}
OutputS1M2& OutputS1M2::setGain(float value)
{
    ui->widgetLinkedVSlider->setValue(value);
    return *this;
}
OutputS1M2& OutputS1M2::setGainLock(bool state)
{
    ui->widgetLinkedVSlider->setEnabled(!state);
    return *this;
}
OutputS1M2& OutputS1M2::setMuteAffectGain(bool state)
{
    mMuteAffectGain = state;
    return *this;
}
OutputS1M2& OutputS1M2::setGainAffectMute(bool state)
{
    mGainAffectMute = state;
    return *this;
}
OutputS1M2& OutputS1M2::setGainRange(int valueStart, int valueEnd05, int valueEnd10, int valueEnd20)
{
    ui->widgetLinkedVSlider->setRange(valueStart, valueEnd05, valueEnd10, valueEnd20);
    ui->widgetUnlinkVSliderLeft->setRange(valueStart, valueEnd05, valueEnd10, valueEnd20);
    ui->widgetUnlinkVSliderRight->setRange(valueStart, valueEnd05, valueEnd10, valueEnd20);
    return *this;
}
OutputS1M2& OutputS1M2::setGainDefault(float value)
{
    ui->widgetLinkedVSlider->setDefault(value);
    return *this;
}
OutputS1M2& OutputS1M2::setGainWidgetDisable(float value)
{
    mDisableGAIN = value;
    return *this;
}
OutputS1M2& OutputS1M2::setAudioSourceDefault(QString audioSourceDefault)
{
    mAudioSourceDefault = audioSourceDefault;
    return *this;
}
OutputS1M2& OutputS1M2::setAudioSourceColor(QColor color)
{
    ui->widgetToolButton->setColor(color);
    return *this;
}
OutputS1M2& OutputS1M2::setChannelNameEditable(bool state)
{
    ui->lineEdit->setEnabled(state);
    return *this;
}
OutputS1M2& OutputS1M2::setValueGAIN(float value)
{
    mPreGAINLeft = value;
    mPreGAINRight = value;
    ui->widgetLinkedVSlider->setValue(mPreGAINLeft);
    ui->widgetUnlinkVSliderLeft->setValue(mPreGAINLeft);
    ui->widgetUnlinkVSliderRight->setValue(mPreGAINRight);
    save("GAIN", mPreGAINLeft);
    save("GAINLeft", mPreGAINLeft);
    save("GAINRight", mPreGAINRight);
    return *this;
}
OutputS1M2& OutputS1M2::setValueMUTE(bool state)
{
    mPreMUTE = state;
    mPreMUTELeft = state;
    mPreMUTERight = state;
    ui->widgetPushButtonGroup1->setState("MUTE", QString::number(mPreMUTE), false);
    ui->widgetPushButtonGroup2->setState("MUTE", QString::number(mPreMUTELeft), false);
    ui->widgetPushButtonGroup3->setState("MUTE", QString::number(mPreMUTERight), false);
    save("MUTE", mPreMUTE);
    save("MUTELeft", mPreMUTELeft);
    save("MUTERight", mPreMUTERight);
    return *this;
}
OutputS1M2& OutputS1M2::setOverlay(bool state)
{
    ui->widgetOverlay->setHidden(!state);
    return *this;
}
OutputS1M2& OutputS1M2::addAudioSource(QString audioClass, QVector<QString>& audioSourceList)
{
    ui->widgetToolButton->addMenu(audioClass);
    for(auto audioSource : audioSourceList)
    {
        ui->widgetToolButton->addAction(audioClass, audioSource);
    }
    return *this;
}
QString OutputS1M2::getAudioSource()
{
    return mPreAudioSource;
}

