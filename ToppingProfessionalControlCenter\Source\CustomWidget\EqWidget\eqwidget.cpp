#include "eqwidget.h"
#include <QScrollBar>
#include <QSizePolicy>
#include <QTimer>
#include <QSpacerItem>
#include <QDebug>
#include "globalfont.h"

EqWidget::EqWidget(QWidget* parent)
    : QWidget(parent)
    , mSizeFactor(1.0)
    , mMainLayoutSpacing(0)
    , mItemsLayoutSpacing(0)
    , mMainLayoutMargins(0, 0, 0, 0)
    , mScrollAreaMargins(0, 0, 0, 0)
    , mMainLayout(nullptr)
    , mScrollArea(nullptr)
    , mScrollWidget(nullptr)
    , mItemsLayout(nullptr)
    , mTitleTypeLabel(nullptr)
    , mTitleGainLabel(nullptr)
    , mTitleFrequencyLabel(nullptr)
    , mTitleQLabel(nullptr)
    , mAddItemButton(nullptr)
    , mRemoveItemButton(nullptr)
    , mMainLeftStretch(2)
    , mLabelAreaStretch(2)
    , mMainSpaceStretch(1)
    , mScrollAreaStretch(44)
    , mMainRightStretch(2)
    , mTitileTopStretch(2)
    , mTitleTypeStretch(1)
    , mTitleGainStretch(3)
    , mTitleFreqStretch(3)
    , mTitleQStretch(3)
    , mTitleBottomStretch(1)
{
    setupUI();
    createDefaultItems();
    setStyleSheet(
        "QWidget {"
        "    background-color: rgb(22,22,22);"
        "}"
        "QLabel {"
        "    color: #404040;"
        "}"
        "QPushButton {"
        "    background-color: #43cf7c;"
        "    color: #ffffff;"
        "}"
        "QPushButton:hover {"
        "    background-color: #52d689;"
        "}"
        "QPushButton:pressed {"
        "    background-color: #3bb56e;"
        "}"
        "QPushButton:disabled {"
        "    background-color: #666666;"
        "    color: #999999;"
        "}"
    );
    mScrollArea->setStyleSheet(
        "QScrollArea {"
        "   background-color: transparent;"
        "}"
        "QScrollBar::sub-line:horizontal {"
        "   width: 0px;"
        "}"
        "QScrollBar::add-line:horizontal {"
        "   width: 0px;"
        "}"
        "QScrollBar::add-page:horizontal, QScrollBar::sub-page:horizontal {"
        "background: none;""}"
    );
}

EqWidget::~EqWidget()
{

}

EqWidget& EqWidget::setName(QString name)
{
    setObjectName(name);
    return *this;
}
EqWidget& EqWidget::setFont(QFont font)
{
    mFont = font;
    resizeEvent(nullptr);
    return *this;
}

void EqWidget::setupUI()
{
    setMinimumSize(600,300);

    mTitleLayout = new QVBoxLayout();
    mTitleLayout->setContentsMargins(0, 0, 0, 0);
    mTitleLayout->setSpacing(0);

    mScrollArea = new QScrollArea(this);
    mScrollArea->setHorizontalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    mScrollArea->setVerticalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    mScrollArea->setWidgetResizable(true);
    mScrollArea->setContentsMargins(mScrollAreaMargins);

    mMainLayout = new QHBoxLayout(this);
    mMainLayout->setContentsMargins(mMainLayoutMargins);
    mMainLayout->setSpacing(mMainLayoutSpacing);
    mMainLayout->addStretch(mMainLeftStretch);
    mMainLayout->addLayout(mTitleLayout, mLabelAreaStretch);
    mMainLayout->addStretch(mMainSpaceStretch);
    mMainLayout->addWidget(mScrollArea, mScrollAreaStretch);
    mMainLayout->addStretch(mMainRightStretch);

    mScrollWidget = new QWidget();
    mItemsLayout = new QHBoxLayout(mScrollWidget);
    mItemsLayout->setSpacing(mItemsLayoutSpacing);
    mItemsLayout->setContentsMargins(0, 0, 0, 0);
    mItemsLayout->addStretch();
    mScrollArea->setWidget(mScrollWidget);

    mTitleTypeLabel = new QLabel("Type", this);
    mTitleTypeLabel->setAlignment(Qt::AlignCenter);
    mTitleGainLabel = new QLabel("Gain", this);
    mTitleGainLabel->setAlignment(Qt::AlignCenter);
    mTitleFrequencyLabel = new QLabel("Freq", this);
    mTitleFrequencyLabel->setAlignment(Qt::AlignCenter);
    mTitleQLabel = new QLabel("Q", this);
    mTitleQLabel->setAlignment(Qt::AlignCenter);
    mTitleLayout->addStretch(mTitileTopStretch);
    mTitleLayout->addWidget(mTitleTypeLabel, mTitleTypeStretch);
    mTitleLayout->addWidget(mTitleGainLabel, mTitleGainStretch);
    mTitleLayout->addWidget(mTitleFrequencyLabel, mTitleFreqStretch);
    mTitleLayout->addWidget(mTitleQLabel, mTitleQStretch);
    mTitleLayout->addStretch(mTitleBottomStretch);
    
    mAddItemButton = new QPushButton("add", this);
    mAddItemButton->setGeometry(0,0, 80, 30);
    connect(mAddItemButton, &QPushButton::clicked, this, [this](){
        addItem();
    });

    mRemoveItemButton = new QPushButton("remove", this);
    mRemoveItemButton->setGeometry(85,0, 80, 30);
    connect(mRemoveItemButton, &QPushButton::clicked, this, [this](){
        removeItem(-1);
    });

    auto comboBox = new ComboBoxS1M3(this);
    comboBox->setGeometry(170,0, 80, 30);
    comboBox->addItems({"1","1.5", "2", "2.5", "3"});
    connect(comboBox, &ComboBoxS1M3::currentIndexChanged, this, [comboBox,this](int index) {
        setSizeFactor(comboBox->itemText(index).toDouble());
    });
}

void EqWidget::adjustFontAndSize()
{
    float hPixelPerRatio=height() / 100.0;
    int marginLeft = hPixelPerRatio * 2;
    int marginBottom = hPixelPerRatio * 3;
    QString style;
    style += QString("QScrollBar::handle:horizontal {"
                        "   background: #43CF7C;"
                        "   min-width: 20px;"
                        "   border-radius: %1px;"
                        "}"
                        "QScrollBar::handle:horizontal:hover {"
                        "   background: #43CF7C;"
                        "}"
                        "QScrollBar::handle:horizontal:pressed {"
                        "   background: #43CF7C;"
                        "}").arg(marginBottom * 0.25);
    style += QString("QScrollBar:horizontal {"
                     "   background: #2B2B2B;"
                     "   height: %1px;"
                     "   border-radius: %2px;"
                     "   margin-top: %3px;"
                     "   margin-bottom: %3px;"
                     "   margin-left: %3px;"
                     "   margin-right: %3px;"
                     "}").arg(marginBottom * 2).arg(marginBottom * 0.25).arg(1.1*marginLeft);
    mScrollArea->horizontalScrollBar()->setStyleSheet(style);
    int bottomMargin = 0;
    if(mScrollArea->horizontalScrollBar()->maximum() > 0)
    {
        bottomMargin = marginBottom * 2;
    }
    mTitleLayout->setContentsMargins(0,0,0, bottomMargin);
    mItemsLayout->setSpacing(height()*0.07);
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, height()*0.05));
    mTitleTypeLabel->setFont(mFont);
    mTitleGainLabel->setFont(mFont);
    mTitleFrequencyLabel->setFont(mFont);
    mTitleQLabel->setFont(mFont);
}

void EqWidget::createDefaultItems()
{
    setItemCount(4);
}

void EqWidget::setSizeFactor(double sizeFactor)
{
    resize(minimumWidth() * sizeFactor, minimumHeight() * sizeFactor);
    adjustFontAndSize();
    QTimer::singleShot(100,[this,sizeFactor](){
        QSize viewportSize = mScrollArea->viewport()->size();
            int bottomMargin = 0;
                float hPixelPerRatio=height() / 100.0;
    int marginLeft = hPixelPerRatio * 2;
    int marginBottom = hPixelPerRatio * 3;
    if(mScrollArea->horizontalScrollBar()->maximum() > 0)
    {
        bottomMargin = marginBottom * 2;
    }
        qDebug()<<viewportSize.height()<<bottomMargin;
        mScrollWidget->setFixedHeight(viewportSize.height()-bottomMargin);
        setItemStretch(sizeFactor);
    });
}

void EqWidget::setItemStretch(double sizeFactor)
{
    for (auto item : mItems) {
        item->setSizeFactor(sizeFactor);
    }
}

void EqWidget::setItemCount(int count)
{
    if (count < 0) count = 0;
    if (count > 32) count = 32;

    int currentCount = mItems.size();

    if (count > currentCount) {
        for (int i = currentCount; i < count; ++i) {
            addItem();
        }
    } else if (count < currentCount) {
        for (int i = currentCount - 1; i >= count; --i) {
            removeItem(i);
        }
    }

    emit ItemCountChanged(count);
}

void EqWidget::addItem()
{
    setUpdatesEnabled(false);

    int index = mItems.size();
    EqWidgetIem* ItemWidget = new EqWidgetIem(index, this);
    ItemWidget->setFont(mFont);
    ItemWidget->setSizeFactor(mSizeFactor);

    connect(ItemWidget, &EqWidgetIem::dataChanged,this, &EqWidget::itemDataChanged);
    connect(ItemWidget, &EqWidgetIem::dataChanged,this, [this](int index, const EqWidgetItemData& data) {
        auto changedAttributes = data.getChangedAttributes();
        for (const auto& attr : changedAttributes) {
            emit attributeChanged(objectName(), attr.first, attr.second);
        }
    });

    mItems.append(ItemWidget);

    ItemWidget->hide();

    mItemsLayout->insertWidget(mItemsLayout->count() - 1, ItemWidget);

    ItemWidget->show();
    setUpdatesEnabled(true);

    mRemoveItemButton->setEnabled(mItems.size() > 1);

    emit ItemCountChanged(mItems.size());
}

void EqWidget::removeItem(int index)
{
    if (mItems.isEmpty()) return;

    if (index < 0 || index >= mItems.size()) {
        index = mItems.size() - 1;
    }

    EqWidgetIem* ItemWidget = mItems.takeAt(index);
    mItemsLayout->removeWidget(ItemWidget);
    ItemWidget->deleteLater();

    updateItemIndices();

    mRemoveItemButton->setEnabled(mItems.size() > 1);

    emit ItemCountChanged(mItems.size());
}

void EqWidget::removeAllItems()
{
    while (!mItems.isEmpty()) {
        removeItem(-1);
    }
}

void EqWidget::updateItemIndices()
{
    for (int i = 0; i < mItems.size(); ++i) {
        mItems[i]->setItemIndex(i);
    }
}

void EqWidget::setEqData(const QVector<EqWidgetItemData>& data)
{
    setItemCount(data.size());

    for (int i = 0; i < data.size() && i < mItems.size(); ++i) {
        mItems[i]->setItemData(data[i]);
    }
}

QVector<EqWidgetItemData> EqWidget::getEqData() const
{
    QVector<EqWidgetItemData> data;
    for (const auto& Item : mItems) {
        data.append(Item->getItemData());
    }
    return data;
}

void EqWidget::showEvent(QShowEvent* event)
{
    QWidget::showEvent(event);
    setSizeFactor(mSizeFactor);
}

void EqWidget::resizeEvent(QResizeEvent* event)
{
    QWidget::resizeEvent(event);
}
