#include "eqwidget.h"
#include <qcombobox.h>
#include <qnamespace.h>
#include <QApplication>
#include <QScrollBar>
#include <QSizePolicy>
#include <QTimer>
#include "globalfont.h"

EqWidgetIem::EqWidgetIem(int index, QWidget* parent)
    : QFrame(parent)
    , mIndex(index)
    , mLayoutSpacing(0)
    , mLayoutMargins(0, 0, 0, 0)
    , mMinimumWidth(50)
    , mLabelStretch(2)
    , mComboStretch(1)
    , mGainStretch(3)
    , mFreqStretch(3)
    , mQStretch(3)
    , mCheckStretch(1)
    , mMainLayout(nullptr)
    , mItemLabel(nullptr)
    , mTypeComboBox(nullptr)
    , mGainDial(nullptr)
    , mFrequencyDial(nullptr)
    , mQValueDial(nullptr)
    , mEnabledCheckBox(nullptr)
{
    mData.type = "high pass";
    mData.gain = 0.0f;
    mData.frequency = 1000.0f;
    mData.qValue = 0.7f;
    mData.enabled = true;

    setupUI();
    setupConnections();
    setStyleSheet(
        "EqWidgetIem {"
        "    background-color: rgb(22,22,22);"
        "}"
        "QLabel {"
        "    color: #404040;"
        "}"
        "QCheckBox::indicator {"
        "    width: 20px;"
        "    height: 20px;"
        "    border-radius: 3px;"
        "}"
        "QCheckBox::indicator:checked {"
        "    background-color: #43cf7c;"
        "}"
        "QCheckBox::indicator:checked {"
        "    image:url(:/Icon/checkboxTick.svg);"
        "}"
        "QCheckBox::indicator:unchecked {"
        "    background-color: #43cf7c;"
        "}"
    );
}

EqWidgetIem::~EqWidgetIem()
{
}

void EqWidgetIem::setupUI()
{
    setMinimumWidth(100);

    mMainLayout = new QVBoxLayout(this);
    mMainLayout->setSpacing(mLayoutSpacing);
    mMainLayout->setContentsMargins(mLayoutMargins);

    mItemLabel = new QLabel(QString::number(mIndex + 1), this);
    mItemLabel->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Fixed);
    mItemLabel->setAlignment(Qt::AlignCenter);
    mMainLayout->addWidget(mItemLabel);
    // QSpacerItem *spacer = new QSpacerItem(0, 0, QSizePolicy::Minimum, QSizePolicy::Expanding);
    // mMainLayout->addSpacerItem(spacer);

    mTypeComboBox = new ComboBoxS1M3(nullptr);
    mTypeComboBox->setIndicatorWHRatio(0.5);
    mTypeComboBox->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Fixed);
    QVector<QString> types = {
        "high pass",
        "Low pass",
        "High Shelf",
        "High Shelf"
    };
    mTypeComboBox->addItems(types);
    mMainLayout->addWidget(mTypeComboBox);
    QSpacerItem *spacer2 = new QSpacerItem(0, 0, QSizePolicy::Minimum, QSizePolicy::Expanding);
    mMainLayout->addSpacerItem(spacer2);

    mGainDial = new DialS1M5(this);
    mGainDial->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Fixed);
    mGainDial->setRange(-20.0f, 20.0f)
              .setValue(0.0f)
              .setStep(0.1f)
              .setPrecision(1)
              .showSign(true);
    mMainLayout->addWidget(mGainDial);
    QSpacerItem *spacer3 = new QSpacerItem(0, 0, QSizePolicy::Minimum, QSizePolicy::Expanding);
    mMainLayout->addSpacerItem(spacer3);

    mFrequencyDial = new DialS1M5(this);
    mFrequencyDial->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Fixed);
    mFrequencyDial->setRange(20.0f, 20000.0f)
                  .setValue(1000.0f)
                  .setStep(1.0f)
                  .setPrecision(0);
    mMainLayout->addWidget(mFrequencyDial);
    QSpacerItem *spacer4 = new QSpacerItem(0, 0, QSizePolicy::Minimum, QSizePolicy::Expanding);
    mMainLayout->addSpacerItem(spacer4);

    mQValueDial = new DialS1M5(this);
    mQValueDial->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Fixed);
    mQValueDial->setRange(0.1f, 10.0f)
                .setValue(0.7f)
                .setStep(0.1f)
                .setPrecision(1);
    mMainLayout->addWidget(mQValueDial);
    QSpacerItem *spacer5 = new QSpacerItem(0, 0, QSizePolicy::Minimum, QSizePolicy::Expanding);
    mMainLayout->addSpacerItem(spacer5);

    mEnabledCheckBox = new QCheckBox(this);
    mEnabledCheckBox->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Fixed);
    mEnabledCheckBox->setChecked(true);
    mMainLayout->addWidget(mEnabledCheckBox, 0, Qt::AlignCenter);
    // QSpacerItem *spacer6 = new QSpacerItem(0, 0, QSizePolicy::Minimum, QSizePolicy::Expanding);
    // mMainLayout->addSpacerItem(spacer6);
}

void EqWidgetIem::setupConnections()
{
    static QHash<EqWidgetItemData::ChangeFlag, std::function<void(const QVariant&)>> changeHandlers = {
        { EqWidgetItemData::TYPE_CHANGED, [this](const QVariant& arg) {
             mData.updateType(mTypeComboBox->currentText());
        }},
        { EqWidgetItemData::GAIN_CHANGED, [this](const QVariant& arg) {
            mData.updateGain(arg.toFloat());
        }},
        { EqWidgetItemData::FREQUENCY_CHANGED, [this](const QVariant& arg) {
            mData.updateFrequency(arg.toFloat());
        }},
        { EqWidgetItemData::QVALUE_CHANGED, [this](const QVariant& arg) {
            mData.updateFrequency(arg.toFloat());
        }},
        { EqWidgetItemData::ENABLED_CHANGED, [this](const QVariant& arg) {
            mData.updateFrequency(arg.toBool());
        }}
    };
    auto* changeHandlersPtr = &changeHandlers;
    static auto handleAndEmit =[this, changeHandlersPtr](EqWidgetItemData::ChangeFlag flag, const QVariant& arg){
        changeHandlersPtr->value(flag)(arg);
        emit dataChanged(mIndex, mData);
        mData.clearAllChanged();
    };
    auto* handleAndEmitPtr = &handleAndEmit;

    connect(mTypeComboBox, &ComboBoxS1M3::currentIndexChanged, this, [handleAndEmitPtr](int index) {
        handleAndEmit(EqWidgetItemData::TYPE_CHANGED, QVariant(index));
    });
    connect(mGainDial, &DialS1M5::valueChanged, this, [handleAndEmitPtr](float value) {
        handleAndEmit(EqWidgetItemData::GAIN_CHANGED, QVariant(value));
    });
    connect(mFrequencyDial, &DialS1M5::valueChanged, this, [handleAndEmitPtr](float value) {
        handleAndEmit(EqWidgetItemData::FREQUENCY_CHANGED, QVariant(value));
    });
    connect(mQValueDial, &DialS1M5::valueChanged, this, [handleAndEmitPtr](float value) {
        handleAndEmit(EqWidgetItemData::QVALUE_CHANGED, QVariant(value));
    });
    connect(mEnabledCheckBox, &QCheckBox::toggled, this, [handleAndEmitPtr](bool enabled) {
        handleAndEmit(EqWidgetItemData::ENABLED_CHANGED, QVariant(enabled));
    });
}

EqWidgetIem& EqWidgetIem::setFont(QFont font)
{
    mFont = font;
    mTypeComboBox->setFont(mFont);
    mGainDial->setFont(mFont);
    mFrequencyDial->setFont(mFont);
    mQValueDial->setFont(mFont);
    resizeEvent(nullptr);
    return *this;
}

void EqWidgetIem::setSizeFactor(double sizeFactor)
{
    setFixedWidth(mMinimumWidth * sizeFactor);
    adjustFontAndSize();
}

void EqWidgetIem::setItemData(const EqWidgetItemData& data)
{
    mData = data;
    mTypeComboBox->setCurrentText(data.type);
    mGainDial->setValue(data.gain);
    mFrequencyDial->setValue(data.frequency);
    mQValueDial->setValue(data.qValue);
    mEnabledCheckBox->setChecked(data.enabled);
}

EqWidgetItemData EqWidgetIem::getItemData() const
{
    return mData;
}

void EqWidgetIem::setItemIndex(int index)
{
    mIndex = index;
    mItemLabel->setText(QString::number(index + 1));
}

void EqWidgetIem::setMinimumItemWidth(int width)
{
    mMinimumWidth = width;
    setMinimumWidth(width);
}

void EqWidgetIem::resizeEvent(QResizeEvent* event)
{
    QWidget::resizeEvent(event);
}

void EqWidgetIem::adjustFontAndSize()
{
    mEnabledCheckBox->setStyleSheet(QString(
    "QCheckBox::indicator {"
        "width: %1px;"
        "height: %1px;"
        "border-radius: %2px;"
        "}"
        "QCheckBox::indicator:checked {"
        "    background-color: #43cf7c;"
        "}"
        "QCheckBox::indicator:checked {"
        "    image:url(:/Icon/checkboxTick.svg);"
        "}"
        "QCheckBox::indicator:unchecked {"
        "    background-color: #43cf7c;"
        "}").arg(mEnabledCheckBox->height(), mEnabledCheckBox->height()*0.2)
    );
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, height()*0.08));
    mItemLabel->setFont(mFont);
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, height()*0.04));

    double perHeight = height()/15.0;
    mItemLabel->setFixedHeight(perHeight*1.5);
    mTypeComboBox->setFixedHeight(perHeight*1.5);
    mGainDial->setFixedHeight(perHeight*3);
    mFrequencyDial->setFixedHeight(perHeight*3);
    mQValueDial->setFixedHeight(perHeight*3);
    mEnabledCheckBox->setFixedHeight(perHeight);
}

EqWidget::EqWidget(QWidget* parent)
    : QWidget(parent)
    , mSizeFactor(1.0)
    , mMainLayoutSpacing(0)
    , mItemsLayoutSpacing(0)
    , mMainLayoutMargins(0, 0, 0, 0)
    , mScrollAreaMargins(0, 0, 0, 0)
    , mMainLayout(nullptr)
    , mScrollArea(nullptr)
    , mScrollWidget(nullptr)
    , mItemsLayout(nullptr)
    , mTitleTypeLabel(nullptr)
    , mTitleGainLabel(nullptr)
    , mTitleFrequencyLabel(nullptr)
    , mTitleQLabel(nullptr)
    , mAddItemButton(nullptr)
    , mRemoveItemButton(nullptr)
    , mMainLeftStretch(2)
    , mLabelAreaStretch(2)
    , mMainSpaceStretch(1)
    , mScrollAreaStretch(44)
    , mMainRightStretch(2)
    , mTitileTopStretch(2)
    , mTitleTypeStretch(1)
    , mTitleGainStretch(3)
    , mTitleFreqStretch(3)
    , mTitleQStretch(3)
    , mTitleBottomStretch(1)
{
    setupUI();
    createDefaultItems();
    setStyleSheet(
        "QWidget {"
        "    background-color: rgb(22,22,22);"
        "}"
        "QLabel {"
        "    color: #404040;"
        "}"
        "QPushButton {"
        "    background-color: #43cf7c;"
        "    color: #ffffff;"
        "}"
        "QPushButton:hover {"
        "    background-color: #52d689;"
        "}"
        "QPushButton:pressed {"
        "    background-color: #3bb56e;"
        "}"
        "QPushButton:disabled {"
        "    background-color: #666666;"
        "    color: #999999;"
        "}"
    );
    mScrollArea->setStyleSheet(
        "QScrollArea {"
        "   background-color: transparent;"
        "}"
        "QScrollBar::sub-line:horizontal {"
        "   width: 0px;"
        "}"
        "QScrollBar::add-line:horizontal {"
        "   width: 0px;"
        "}"
        "QScrollBar::add-page:horizontal, QScrollBar::sub-page:horizontal {"
        "background: none;""}"
    );
}

EqWidget::~EqWidget()
{

}

EqWidget& EqWidget::setName(QString name)
{
    setObjectName(name);
    return *this;
}
EqWidget& EqWidget::setFont(QFont font)
{
    mFont = font;
    resizeEvent(nullptr);
    return *this;
}

void EqWidget::setupUI()
{
    setMinimumSize(600,300);

    mTitleLayout = new QVBoxLayout();
    mTitleLayout->setContentsMargins(0, 0, 0, 0);
    mTitleLayout->setSpacing(0);

    mScrollArea = new QScrollArea(this);
    mScrollArea->setHorizontalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    mScrollArea->setVerticalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    mScrollArea->setWidgetResizable(true);
    mScrollArea->setContentsMargins(mScrollAreaMargins);

    mMainLayout = new QHBoxLayout(this);
    mMainLayout->setContentsMargins(mMainLayoutMargins);
    mMainLayout->setSpacing(mMainLayoutSpacing);
    mMainLayout->addStretch(mMainLeftStretch);
    mMainLayout->addLayout(mTitleLayout, mLabelAreaStretch);
    mMainLayout->addStretch(mMainSpaceStretch);
    mMainLayout->addWidget(mScrollArea, mScrollAreaStretch);
    mMainLayout->addStretch(mMainRightStretch);

    mScrollWidget = new QWidget();
    mItemsLayout = new QHBoxLayout(mScrollWidget);
    mItemsLayout->setSpacing(mItemsLayoutSpacing);
    mItemsLayout->setContentsMargins(0, 0, 0, 0);
    mItemsLayout->addStretch();
    mScrollArea->setWidget(mScrollWidget);

    mTitleTypeLabel = new QLabel("Type", this);
    mTitleTypeLabel->setAlignment(Qt::AlignCenter);
    mTitleGainLabel = new QLabel("Gain", this);
    mTitleGainLabel->setAlignment(Qt::AlignCenter);
    mTitleFrequencyLabel = new QLabel("Freq", this);
    mTitleFrequencyLabel->setAlignment(Qt::AlignCenter);
    mTitleQLabel = new QLabel("Q", this);
    mTitleQLabel->setAlignment(Qt::AlignCenter);
    mTitleLayout->addStretch(mTitileTopStretch);
    mTitleLayout->addWidget(mTitleTypeLabel, mTitleTypeStretch);
    mTitleLayout->addWidget(mTitleGainLabel, mTitleGainStretch);
    mTitleLayout->addWidget(mTitleFrequencyLabel, mTitleFreqStretch);
    mTitleLayout->addWidget(mTitleQLabel, mTitleQStretch);
    mTitleLayout->addStretch(mTitleBottomStretch);
    
    mAddItemButton = new QPushButton("add", this);
    mAddItemButton->setGeometry(0,0, 80, 30);
    connect(mAddItemButton, &QPushButton::clicked, this, [this](){
        addItem();
    });

    mRemoveItemButton = new QPushButton("remove", this);
    mRemoveItemButton->setGeometry(85,0, 80, 30);
    connect(mRemoveItemButton, &QPushButton::clicked, this, [this](){
        removeItem(-1);
    });

    auto comboBox = new ComboBoxS1M3(this);
    comboBox->setGeometry(170,0, 80, 30);
    comboBox->addItems({"1","1.5", "2", "2.5", "3"});
    connect(comboBox, &ComboBoxS1M3::currentIndexChanged, this, [comboBox,this](int index) {
        setSizeFactor(comboBox->itemText(index).toDouble());
    });
}

void EqWidget::adjustFontAndSize()
{
    float hPixelPerRatio=height() / 100.0;
    int marginLeft = hPixelPerRatio * 2;
    int marginBottom = hPixelPerRatio * 3;
    QString style;
    style += QString("QScrollBar::handle:horizontal {"
                        "   background: #43CF7C;"
                        "   min-width: 20px;"
                        "   border-radius: %1px;"
                        "}"
                        "QScrollBar::handle:horizontal:hover {"
                        "   background: #43CF7C;"
                        "}"
                        "QScrollBar::handle:horizontal:pressed {"
                        "   background: #43CF7C;"
                        "}").arg(marginBottom * 0.25);
    style += QString("QScrollBar:horizontal {"
                     "   background: #2B2B2B;"
                     "   height: %1px;"
                     "   border-radius: %2px;"
                     "   margin-top: %3px;"
                     "   margin-bottom: %3px;"
                     "   margin-left: %3px;"
                     "   margin-right: %3px;"
                     "}").arg(marginBottom * 2).arg(marginBottom * 0.25).arg(1.1*marginLeft);
    mScrollArea->horizontalScrollBar()->setStyleSheet(style);
    int bottomMargin = 0;
    if(mScrollArea->horizontalScrollBar()->maximum() > 0)
    {
        bottomMargin = marginBottom * 2;
    }
    mTitleLayout->setContentsMargins(0,0,0, bottomMargin);
    mItemsLayout->setSpacing(height()*0.07);
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, height()*0.05));
    mTitleTypeLabel->setFont(mFont);
    mTitleGainLabel->setFont(mFont);
    mTitleFrequencyLabel->setFont(mFont);
    mTitleQLabel->setFont(mFont);
}

void EqWidget::createDefaultItems()
{
    setItemCount(4);
}

void EqWidget::setSizeFactor(double sizeFactor)
{
    resize(minimumWidth() * sizeFactor, minimumHeight() * sizeFactor);
    adjustFontAndSize();
    QTimer::singleShot(100,[this,sizeFactor](){
        QSize viewportSize = mScrollArea->viewport()->size();
            int bottomMargin = 0;
                float hPixelPerRatio=height() / 100.0;
    int marginLeft = hPixelPerRatio * 2;
    int marginBottom = hPixelPerRatio * 3;
    if(mScrollArea->horizontalScrollBar()->maximum() > 0)
    {
        bottomMargin = marginBottom * 2;
    }
        qDebug()<<viewportSize.height()<<bottomMargin;
        mScrollWidget->setFixedHeight(viewportSize.height()-bottomMargin);
        setItemStretch(sizeFactor);
    });
}

void EqWidget::setItemStretch(double sizeFactor)
{
    for (auto item : mItems) {
        item->setSizeFactor(sizeFactor);
    }
}

void EqWidget::setItemCount(int count)
{
    if (count < 0) count = 0;
    if (count > 32) count = 32;

    int currentCount = mItems.size();

    if (count > currentCount) {
        for (int i = currentCount; i < count; ++i) {
            addItem();
        }
    } else if (count < currentCount) {
        for (int i = currentCount - 1; i >= count; --i) {
            removeItem(i);
        }
    }

    emit ItemCountChanged(count);
}

void EqWidget::addItem()
{
    setUpdatesEnabled(false);

    int index = mItems.size();
    EqWidgetIem* ItemWidget = new EqWidgetIem(index, this);
    ItemWidget->setFont(mFont);
    ItemWidget->setSizeFactor(mSizeFactor);

    connect(ItemWidget, &EqWidgetIem::dataChanged,this, &EqWidget::itemDataChanged);
    connect(ItemWidget, &EqWidgetIem::dataChanged,this, [this](int index, const EqWidgetItemData& data) {
        auto changedAttributes = data.getChangedAttributes();
        for (const auto& attr : changedAttributes) {
            emit attributeChanged(objectName(), attr.first, attr.second);
        }
    });

    mItems.append(ItemWidget);

    ItemWidget->hide();

    mItemsLayout->insertWidget(mItemsLayout->count() - 1, ItemWidget);

    ItemWidget->show();
    setUpdatesEnabled(true);

    mRemoveItemButton->setEnabled(mItems.size() > 1);

    emit ItemCountChanged(mItems.size());
}

void EqWidget::removeItem(int index)
{
    if (mItems.isEmpty()) return;

    if (index < 0 || index >= mItems.size()) {
        index = mItems.size() - 1;
    }

    EqWidgetIem* ItemWidget = mItems.takeAt(index);
    mItemsLayout->removeWidget(ItemWidget);
    ItemWidget->deleteLater();

    updateItemIndices();

    mRemoveItemButton->setEnabled(mItems.size() > 1);

    emit ItemCountChanged(mItems.size());
}

void EqWidget::removeAllItems()
{
    while (!mItems.isEmpty()) {
        removeItem(-1);
    }
}

void EqWidget::updateItemIndices()
{
    for (int i = 0; i < mItems.size(); ++i) {
        mItems[i]->setItemIndex(i);
    }
}

void EqWidget::setEqData(const QVector<EqWidgetItemData>& data)
{
    setItemCount(data.size());

    for (int i = 0; i < data.size() && i < mItems.size(); ++i) {
        mItems[i]->setItemData(data[i]);
    }
}

QVector<EqWidgetItemData> EqWidget::getEqData() const
{
    QVector<EqWidgetItemData> data;
    for (const auto& Item : mItems) {
        data.append(Item->getItemData());
    }
    return data;
}

void EqWidget::showEvent(QShowEvent* event)
{
    QWidget::showEvent(event);
    setSizeFactor(mSizeFactor);
}

void EqWidget::resizeEvent(QResizeEvent* event)
{
    QWidget::resizeEvent(event);
}