#include "inputs1m5.h"
#include "globalfont.h"
#include "ui_inputs1m5.h"


InputS1M5::InputS1M5(QWidget* parent, QString name)
    : InputBase(parent)
    , WorkspaceObserver(name)
    , AppSettingsObserver(name)
    , ui(new Ui::InputS1M5)
{
    ui->setupUi(this);
    installEventFilter(this);
    resize(minimumWidth(), minimumHeight());
    QString style;
    style = "QFrame {"
            "   background-color: #161616;"
            "   border-radius: 8px;"
            "}";
    ui->frame->setStyleSheet(style);
    style = "QWidget {"
            "   background-color: rgba(31, 31, 31, 153);"
            "   border-radius: 8px;"
            "}";
    ui->widgetOverlay->setStyleSheet(style);
    ui->widgetOverlay->setAttribute(Qt::WA_TransparentForMouseEvents);
    ui->widgetOverlay->setHidden(true);
    style = "QLineEdit {"
            "   color: rgb(161, 161, 161);"
            "   background-color: rgb(46, 46, 46);"
            "   border-radius: 8px;"
            "   selection-color: rgb(0, 121, 107);"
            "   selection-background-color: rgb(224, 247, 250);"
            "}";
    ui->lineEdit->setStyleSheet(style);
    ui->lineEdit->setAttribute(Qt::WA_Hover);
    ui->lineEdit->installEventFilter(this);
    style = "QPushButton {"
            "   background-color: transparent;"
            "   image: url(:/Icon/WidgetCloseBlack.png);"
            "}"
            "QPushButton:hover {"
            "   border: 2px solid rgb(46, 46, 46);"
            "   border-radius: 3px;"
            "}";
    ui->pushButtonClose->setStyleSheet(style);
    ui->pushButtonClose->setParent(this);
    ui->pushButtonClose->setFocusPolicy(Qt::NoFocus);
    ui->pushButtonClose->hide();
    ui->pushButtonClose->setAttribute(Qt::WA_Hover);
    ui->pushButtonClose->installEventFilter(this);
    ui->widgetVolumeMeter->setWidthRatio(23, 30, 6, 35);
    ui->widgetVolumeMeter->setHeightRatio(8, 3, 2, 2, 85, 0);
    ui->widgetVolumeMeter->setScaleLineHidden(true);
    ui->widgetDial->showCircle(false).showSign(true);
    mAutoGain.setModal(true);
    mAutoGain.setMovable(false);
    mAutoGain.setResizable(false);
    mTimer.setSingleShot(true);
    mTimer.setInterval(100);
    connect(&mAutoGain, SIGNAL(attributeChanged(QString, QString)), this, SLOT(in_mAutoGain_attributeChanged(QString, QString)), Qt::UniqueConnection);
    connect(&mTimer, SIGNAL(timeout()), this, SLOT(in_mTimer_timeout()), Qt::UniqueConnection);
    connect(ui->widgetPushButtonGroup1, SIGNAL(stateChanged(QString, QString)), this, SLOT(in_widgetPushButtonGroup1_stateChanged(QString, QString)), Qt::UniqueConnection);
    connect(ui->widgetPushButtonGroup2, SIGNAL(stateChanged(QString, QString)), this, SLOT(in_widgetPushButtonGroup2_stateChanged(QString, QString)), Qt::UniqueConnection);
    connect(ui->widgetDial, SIGNAL(valueChanged(float)), this, SLOT(in_widgetDial_valueChanged(float)), Qt::UniqueConnection);
}
InputS1M5::~InputS1M5()
{
    delete ui;
}


// override
bool InputS1M5::eventFilter(QObject* obj, QEvent* e)
{
    if((obj == ui->lineEdit || obj == ui->pushButtonClose) && isWidgetEnable() && isWidgetMovable())
    {
        if(e->type() == QEvent::HoverEnter)
        {
            if(ui->pushButtonClose->isHidden())
            {
                mTimer.start();
            }
            return true;
        }
        else if(e->type() == QEvent::HoverLeave)
        {
            QTimer::singleShot(0, [this](){
                if(!ui->lineEdit->underMouse() && !ui->pushButtonClose->underMouse())
                {
                    mTimer.stop();
                    ui->pushButtonClose->hide();
                }
            });
        }
        if(obj == ui->lineEdit && e->type() == QEvent::MouseButtonPress && ui->lineEdit->isEnabled())
        {
            mTimer.stop();
            ui->pushButtonClose->hide();
        }
    }
    else if(obj == this && isWidgetEnable() && isWidgetMovable())
    {
        if(e->type() == QEvent::Move)
        {
            QTimer::singleShot(0, [this](){
                if(ui->lineEdit->geometry().contains(ui->frame->mapFromGlobal(QCursor::pos())))
                {
                    mTimer.start();
                }
            });
        }
    }
    return QWidget::eventFilter(obj, e);
}
void InputS1M5::resizeEvent(QResizeEvent* e)
{
    Q_UNUSED(e);
    // W
    float wPixelPerRatio=size().width() / 100.0;
    int wSpace1=wPixelPerRatio * 8;
    int wMeter=wPixelPerRatio * 32;
    int wSpace2=wPixelPerRatio * 8;
    int wButtonGroup1=wPixelPerRatio * 42;
    int xMeter=wSpace1;
    int xButtonGroup1=wSpace1 + wMeter + wSpace2;
    // H
    float hPixelPerRatio = size().height() / 100.0;
    int hLineEdit=hPixelPerRatio * 8;
    int hSpace1=hPixelPerRatio * 0;
    int hMeter=hPixelPerRatio * 50;
    int hSpace2=hPixelPerRatio * 1;
    int hDial=hPixelPerRatio * 19;
    int hSpace3=hPixelPerRatio * 1;
    int hButtonGroup2=hPixelPerRatio * 20;
    int hPushButtonClose=hLineEdit / 100.0 * 50;
    ui->lineEdit->setGeometry(0, 0, size().width(), hLineEdit);
    ui->pushButtonClose->setGeometry(size().width() - hPushButtonClose * 1.3, (hLineEdit - hPushButtonClose) / 2, hPushButtonClose, hPushButtonClose);
    ui->widgetVolumeMeter->setGeometry(xMeter, hLineEdit + hSpace1, wMeter, hMeter);
    ui->widgetPushButtonGroup1->setGeometry(wPixelPerRatio * 38, hPixelPerRatio * 37, wPixelPerRatio * 60, wPixelPerRatio * 60 / ui->widgetPushButtonGroup1->minimumWidth() * ui->widgetPushButtonGroup1->minimumHeight());
    ui->widgetDial->setGeometry(0, hLineEdit + hSpace1 + hMeter + hSpace2, size().width(), hDial);
    ui->widgetPushButtonGroup2->setGeometry(wPixelPerRatio * 20.1, hPixelPerRatio * 75.5, size().width() - wPixelPerRatio * 40.2, (size().width() - wPixelPerRatio * 40.2) / ui->widgetPushButtonGroup2->minimumWidth() * ui->widgetPushButtonGroup2->minimumHeight());
    ui->widgetOverlay->setGeometry(rect().x(), rect().y(), rect().width(), hLineEdit);
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, ui->lineEdit->height()) - 3);
    if(mFont.pointSize() < 8)
    {
        mFont.setPointSize(mFont.pointSize());
    }
    else if(mFont.pointSize() < 12)
    {
        mFont.setPointSize(mFont.pointSize() - 1);
    }
    else if(mFont.pointSize() < 17)
    {
        mFont.setPointSize(mFont.pointSize() - 2);
    }
    else if(mFont.pointSize() < 22)
    {
        mFont.setPointSize(mFont.pointSize() - 3);
    }
    mFont.setPointSize(mFont.pointSize() - 1);
    ui->lineEdit->setFont(mFont);
    int radius=size().width() * 0.04;
    QString style;
    style = QString("QFrame {"
                    "   background-color: #161616;"
                    "   border-radius: %1px;"
                    "}").arg(radius);
    ui->frame->setStyleSheet(style);
    style = QString("QWidget {"
                    "   background-color: rgba(31, 31, 31, 153);"
                    "   border-top-left-radius: %1px; border-top-right-radius: %1px;"
                    "}").arg(radius);
    ui->widgetOverlay->setStyleSheet(style);
    style = QString("QLineEdit {"
                    "   color: rgb(161, 161, 161);"
                    "   background-color: rgb(46, 46, 46);"
                    "   border-top-left-radius: %1px; border-top-right-radius: %1px;"
                    "   selection-color: rgb(0, 121, 107);"
                    "   selection-background-color: rgb(224, 247, 250);"
                    "}").arg(radius);
    ui->lineEdit->setStyleSheet(style);
}
void InputS1M5::updateAttribute()
{
    if(isWidgetReady())
    {
        float gain;
        if(isWidgetEnable())
        {
            if(mMuteAffectGain)
            {
                gain = ui->widgetPushButtonGroup2->getState("MUTE").toInt() ? (mDisableGAIN) : (ui->widgetDial->getValue());
            }
            else
            {
                gain = ui->widgetDial->getValue();
            }
            if(mPre48V != ui->widgetPushButtonGroup1->getState("48V").toInt())
            {
                mPre48V = ui->widgetPushButtonGroup1->getState("48V").toInt();
                emit attributeChanged(this->objectName(), "48V", QString::number(mPre48V));
            }
            if(mPreGAIN != gain)
            {
                mPreGAIN = gain;
                emit attributeChanged(this->objectName(), "GAIN", QString::number(mPreGAIN));
            }
            if(mPreMUTE != ui->widgetPushButtonGroup2->getState("MUTE").toInt())
            {
                mPreMUTE = ui->widgetPushButtonGroup2->getState("MUTE").toInt();
                emit attributeChanged(this->objectName(), "MUTE", QString::number(mPreMUTE));
            }
            if(mPreANTI != ui->widgetPushButtonGroup2->getState("ANTI").toInt())
            {
                mPreANTI = ui->widgetPushButtonGroup2->getState("ANTI").toInt();
                emit attributeChanged(this->objectName(), "ANTI", QString::number(mPreANTI));
            }
        }
        else
        {
            if(mMuteAffectGain)
            {
                gain = mDisableGAIN;
            }
            else
            {
                gain = ui->widgetDial->getValue();
            }
            if(mPreGAIN != gain)
            {
                mPreGAIN = gain;
                emit attributeChanged(this->objectName(), "GAIN", QString::number(mPreGAIN));
            }
            if(mPreMUTE != static_cast<int>(true))
            {
                mPreMUTE = static_cast<int>(true);
                emit attributeChanged(this->objectName(), "MUTE", QString::number(mPreMUTE));
            }
            if(mPreANTI != static_cast<int>(false))
            {
                mPreANTI = static_cast<int>(false);
                emit attributeChanged(this->objectName(), "ANTI", QString::number(mPreANTI));
            }
        }
    }
}
void InputS1M5::setSoloState(bool state)
{
    ui->widgetPushButtonGroup2->setState("SOLO", QString::number(state), false);
}
void InputS1M5::setSoloStateLeft(bool state)
{
    Q_UNUSED(state);
}
void InputS1M5::setSoloStateRight(bool state)
{
    Q_UNUSED(state);
}
void InputS1M5::setMuteState(bool state)
{
    ui->widgetPushButtonGroup2->setState("MUTE", QString::number(state), false);
}
void InputS1M5::setMuteStateLeft(bool state)
{
    Q_UNUSED(state);
}
void InputS1M5::setMuteStateRight(bool state)
{
    Q_UNUSED(state);
}
void InputS1M5::setSoloClicked(bool state)
{
    ui->widgetPushButtonGroup2->setState("SOLO", QString::number(state), true);
}
void InputS1M5::setSoloClickedLeft(bool state)
{
    Q_UNUSED(state);
}
void InputS1M5::setSoloClickedRight(bool state)
{
    Q_UNUSED(state);
}
void InputS1M5::setMuteClicked(bool state)
{
    ui->widgetPushButtonGroup2->setState("MUTE", QString::number(state), true);
}
void InputS1M5::setMuteClickedLeft(bool state)
{
    Q_UNUSED(state);
}
void InputS1M5::setMuteClickedRight(bool state)
{
    Q_UNUSED(state);
}
bool InputS1M5::getSoloState()
{
    return (bool) ui->widgetPushButtonGroup2->getState("SOLO").toInt();
}
bool InputS1M5::getSoloStateLeft()
{
    return false;
}
bool InputS1M5::getSoloStateRight()
{
    return false;
}
bool InputS1M5::getMuteState()
{
    return (bool) ui->widgetPushButtonGroup2->getState("MUTE").toInt();
}
bool InputS1M5::getMuteStateLeft()
{
    return false;
}
bool InputS1M5::getMuteStateRight()
{
    return false;
}
void InputS1M5::loadSettings()
{
    mPre48V = -2147483648;
    mPreMUTE = -2147483648;
    mPreANTI = -2147483648;
    mPreGAIN = -2147483648;
    setWidgetReady(false);
    WorkspaceObserver::getSettings()->beginGroup(objectName());
    bool flag=WorkspaceObserver::getSettings()->contains(objectName());
    WorkspaceObserver::getSettings()->endGroup();
    if(!flag)
    {
        WorkspaceObserver::setValue(objectName(), true);
        WorkspaceObserver::setValue("ChannelName", getChannelName());
        WorkspaceObserver::setValue("48V", false);
        WorkspaceObserver::setValue("ANTI", false);
        WorkspaceObserver::setValue("SOLO", false);
        WorkspaceObserver::setValue("MUTE", false);
        WorkspaceObserver::setValue("GAIN", ui->widgetDial->getDefault());
    }
    ui->lineEdit->setText(WorkspaceObserver::value("ChannelName").toString());
    ui->widgetPushButtonGroup1->setState("48V", QString::number(WorkspaceObserver::value("48V").toBool()), false);
    ui->widgetPushButtonGroup2->setState("ANTI", QString::number(WorkspaceObserver::value("ANTI").toBool()), false);
    ui->widgetDial->setValue(WorkspaceObserver::value("GAIN").toFloat());
    // must load SOLO & MUTE at the end
    loadSoloMuteState(true,
                      WorkspaceObserver::value("SOLO").toBool(),
                      false,
                      false,
                      WorkspaceObserver::value("MUTE").toBool(),
                      false,
                      false
                      );
    if(isWidgetEmitAction())
    {
        emit attributeChanged(this->objectName(), "Save_ChannelName", WorkspaceObserver::value("ChannelName").toString());
        emit attributeChanged(this->objectName(), "Save_Enable", QString::number(isWidgetEnable()));
        emit attributeChanged(this->objectName(), "Save_48V", QString::number(WorkspaceObserver::value("48V").toBool()));
        emit attributeChanged(this->objectName(), "Save_ANTI", QString::number(WorkspaceObserver::value("ANTI").toBool()));
        emit attributeChanged(this->objectName(), "Save_SOLO", QString::number(WorkspaceObserver::value("SOLO").toBool()));
        emit attributeChanged(this->objectName(), "Save_MUTE", QString::number(WorkspaceObserver::value("MUTE").toBool()));
        emit attributeChanged(this->objectName(), "Save_GAIN", WorkspaceObserver::value("GAIN").toString());
    }
    setWidgetReady(true);
    updateAttribute();
}
void InputS1M5::AppSettingsChanged(QString objectName, QString attribute, QString value)
{
    Q_UNUSED(objectName);
    Q_UNUSED(attribute);
    Q_UNUSED(value);
}


// slot
void InputS1M5::in_mAutoGain_attributeChanged(QString attribute, QString value)
{
    if(attribute == "Start")
    {
        float gain = ui->widgetDial->getValue();
        ui->widgetDial->setValue(0);
        in_widgetDial_valueChanged(ui->widgetDial->getValue());
        save("GAIN", gain);
    }
}
void InputS1M5::in_mTimer_timeout()
{
    ui->pushButtonClose->show();
}
void InputS1M5::in_widgetPushButtonGroup1_stateChanged(QString button, QString state)
{
    if(button == "48V")
    {
        save("48V", state);
        updateAttribute();
    }
    else if(button == "AUTO")
    {
        QPoint pointCenter=APPSHandle.getMainWindow()->geometry().center();
        int w=APPSHandle.getMainWindow()->height() / 3;
        int h=w;
        mAutoGain.setGeometry(pointCenter.x() - w / 2, pointCenter.y() - h / 2, w, h);
        int gain = mAutoGain.exec();
        if(gain != -2147483648)
        {
            ui->widgetDial->setValue(-6 - (gain / 10 - 1));
            in_widgetDial_valueChanged(ui->widgetDial->getValue());
        }
        else
        {
            ui->widgetDial->setValue(WorkspaceObserver::value("GAIN").toFloat());
            in_widgetDial_valueChanged(ui->widgetDial->getValue());
        }
        ui->widgetPushButtonGroup1->setState("AUTO", "0", false);
    }
    else if(button == "DUCKING")
    {
        emit attributeChanged(this->objectName(), "Ducking", state);
    }
}
void InputS1M5::in_widgetPushButtonGroup2_stateChanged(QString button, QString state)
{
    if(button == "SOLO")
    {
        if(doSolo())
        {
            save("SOLO", (bool) state.toInt());
        }
    }
    else if(button == "MUTE")
    {
        if(doMute())
        {
            save("MUTE", (bool) state.toInt());
        }
        updateAttribute();
    }
    else if(button == "ANTI")
    {
        save("ANTI", (bool) state.toInt());
        updateAttribute();
    }
}
void InputS1M5::in_widgetDial_valueChanged(float value)
{
    save("GAIN", value);
    updateAttribute();
    if(mGainAffectMute && ui->widgetPushButtonGroup2->getState("MUTE").toInt())
    {
        ui->widgetPushButtonGroup2->setState("MUTE", QString::number(false), true);
    }
}
void InputS1M5::on_lineEdit_textChanged(const QString& arg1)
{
    QFont font=ui->lineEdit->font();
    font.setPointSize(5);
    if(!GLBFHandle.isSuitable(font, arg1, minimumWidth() - 6))
    {
        ui->lineEdit->setText(arg1.chopped(1));
    }
}
void InputS1M5::on_lineEdit_editingFinished()
{
    if(ui->lineEdit->text().isEmpty())
    {
        ui->lineEdit->setText(getChannelName());
    }
    ui->lineEdit->clearFocus();
    save("ChannelName", ui->lineEdit->text());
}
void InputS1M5::on_pushButtonClose_clicked()
{
    emit attributeChanged(getChannelName(), "Hide", QString::number(static_cast<int>(true)));
    ui->pushButtonClose->hide();
}


// setter & getter
void InputS1M5::save(QAnyStringView key, const QVariant& value)
{
    WorkspaceObserver::setValue(key, value);
    if(isWidgetEmitAction())
    {
        emit attributeChanged(this->objectName(), "Save_" + key.toString(), value.typeId() == QMetaType::Bool ? (QString::number(value.toBool())) : (value.toString()));
    }
}
InputS1M5& InputS1M5::setName(QString name)
{
    setObjectName(name);
    WorkspaceObserver::setObserverName(name);
    AppSettingsObserver::setObserverName(name);
    return *this;
}
InputS1M5& InputS1M5::setFont(QFont font)
{
    mFont = font;
    ui->widgetVolumeMeter->setFont(font);
    ui->widgetPushButtonGroup1->setFont(font);
    ui->widgetDial->setFont(font);
    ui->widgetPushButtonGroup2->setFont(font);
    mAutoGain.setFont(mFont);
    QResizeEvent e(size(), size());
    resizeEvent(&e);
    update();
    return *this;
}
InputS1M5& InputS1M5::setVolumeMeter(int value)
{
    ui->widgetVolumeMeter->setValue(value);
    mAutoGain.sampleLevel(value / 10.0);
    return *this;
}
InputS1M5& InputS1M5::setVolumeMeterClear()
{
    ui->widgetVolumeMeter->setMeterClear();
    return *this;
}
InputS1M5& InputS1M5::setVolumeMeterSlip()
{
    ui->widgetVolumeMeter->setMeterSlip();
    return *this;
}
InputS1M5& InputS1M5::setGain(float value)
{
    ui->widgetDial->setValue(value);
    return *this;
}
InputS1M5& InputS1M5::setGainLock(bool state)
{
    ui->widgetDial->setMovable(!state);
    return *this;
}
InputS1M5& InputS1M5::setMuteAffectGain(bool state)
{
    mMuteAffectGain = state;
    return *this;
}
InputS1M5& InputS1M5::setGainAffectMute(bool state)
{
    mGainAffectMute = state;
    return *this;
}
InputS1M5& InputS1M5::setGainRange(float min, float max)
{
    ui->widgetDial->setRange(min, max);
    return *this;
}
InputS1M5& InputS1M5::setGainDefault(float value)
{
    ui->widgetDial->setDefault(value);
    return *this;
}
InputS1M5& InputS1M5::setGainWidgetDisable(float value)
{
    mDisableGAIN = value;
    return *this;
}
InputS1M5& InputS1M5::setChannelNameEditable(bool state)
{
    ui->lineEdit->setEnabled(state);
    return *this;
}
InputS1M5& InputS1M5::setValue48V(bool state)
{
    mPre48V = state;
    ui->widgetPushButtonGroup1->setState("48V", QString::number(mPre48V), false);
    save("48V", mPre48V);
    return *this;
}
InputS1M5& InputS1M5::setValueGAIN(float value)
{
    mPreGAIN = value;
    ui->widgetDial->setValue(mPreGAIN);
    save("GAIN", mPreGAIN);
    return *this;
}
InputS1M5& InputS1M5::setValueMUTE(bool state)
{
    mPreMUTE = state;
    ui->widgetPushButtonGroup2->setState("MUTE", QString::number(mPreMUTE), false);
    if(doMute())
    {
        save("MUTE", mPreMUTE);
    }
    return *this;
}
InputS1M5& InputS1M5::setOverlay(bool state)
{
    ui->widgetOverlay->setHidden(!state);
    return *this;
}

