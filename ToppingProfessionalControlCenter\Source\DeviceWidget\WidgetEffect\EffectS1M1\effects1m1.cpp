#include "effects1m1.h"
#include "globalfont.h"
#include "ui_effects1m1.h"


EffectS1M1::EffectS1M1(QWidget* parent, QString name)
    : EffectBase(parent)
    , WorkspaceObserver(name)
    , AppSettingsObserver(name)
    , ui(new Ui::EffectS1M1)
{
    ui->setupUi(this);
    installEventFilter(this);
    resize(minimumWidth(), minimumHeight());
    QString style;
    style = "QFrame {"
            "   background-color: #161616;"
            "   border-radius: 8px;"
            "}";
    ui->frame->setStyleSheet(style);
    style = "QLineEdit {"
            "   color: rgb(161, 161, 161);"
            "   background-color: rgb(46, 46, 46);"
            "   border-radius: 5px;"
            "   selection-color: rgb(0, 121, 107);"
            "   selection-background-color: rgb(224, 247, 250);"
            "}";
    ui->lineEdit->setStyleSheet(style);
    ui->lineEdit->setAttribute(Qt::WA_Hover);
    ui->lineEdit->installEventFilter(this);
    style = "QPushButton {"
            "   background-color: transparent;"
            "   image: url(:/Icon/WidgetCloseBlack.png);"
            "}"
            "QPushButton:hover {"
            "   border: 2px solid rgb(46, 46, 46);"
            "   border-radius: 3px;"
            "}";
    ui->pushButtonClose->setStyleSheet(style);
    ui->pushButtonClose->setParent(this);
    ui->pushButtonClose->setFocusPolicy(Qt::NoFocus);
    ui->pushButtonClose->hide();
    ui->pushButtonClose->setAttribute(Qt::WA_Hover);
    ui->pushButtonClose->installEventFilter(this);
    ui->widgetVolumeMeter->setWidthRatio(23, 30, 6, 35);
    ui->widgetVolumeMeter->setScaleLineHidden(true);
    ui->widgetDial->showCircle(false).setRange(-90, 0).setDefault(0).showInfinitesimal(true);
    mTimer.setSingleShot(true);
    mTimer.setInterval(100);
    connect(&mTimer, SIGNAL(timeout()), this, SLOT(in_mTimer_timeout()), Qt::UniqueConnection);
    connect(ui->widgetPushButtonGroup1, SIGNAL(buttonStateChanged(PushButtonS1M4::ButtonID, bool)), this, SLOT(in_widgetPushButtonGroup1_buttonStateChanged(PushButtonS1M4::ButtonID, bool)), Qt::UniqueConnection);
    connect(ui->widgetDial, SIGNAL(valueChanged(float)), this, SLOT(in_widgetDial_valueChanged(float)), Qt::UniqueConnection);
    connect(ui->widgetPushButtonGroup2, SIGNAL(buttonStateChanged(PushButtonS1M7::ButtonID, bool)), this, SLOT(in_widgetPushButtonGroup2_buttonStateChanged(PushButtonS1M7::ButtonID, bool)), Qt::UniqueConnection);
}
EffectS1M1::~EffectS1M1()
{
    delete ui;
}


// override
bool EffectS1M1::eventFilter(QObject* obj, QEvent* e)
{
    if((obj == ui->lineEdit || obj == ui->pushButtonClose) && isWidgetEnable() && isWidgetMovable())
    {
        if(e->type() == QEvent::HoverEnter)
        {
            if(ui->pushButtonClose->isHidden())
            {
                mTimer.start();
            }
            return true;
        }
        else if(e->type() == QEvent::HoverLeave)
        {
            QTimer::singleShot(0, [this](){
                if(!ui->lineEdit->underMouse() && !ui->pushButtonClose->underMouse())
                {
                    mTimer.stop();
                    ui->pushButtonClose->hide();
                }
            });
        }
        if(obj == ui->lineEdit && e->type() == QEvent::MouseButtonPress && ui->lineEdit->isEnabled())
        {
            mTimer.stop();
            ui->pushButtonClose->hide();
        }
    }
    else if(obj == this && isWidgetEnable() && isWidgetMovable())
    {
        if(e->type() == QEvent::Move)
        {
            QTimer::singleShot(0, [this](){
                if(ui->lineEdit->geometry().contains(ui->frame->mapFromGlobal(QCursor::pos())))
                {
                    mTimer.start();
                }
            });
        }
    }
    return QWidget::eventFilter(obj, e);
}
void EffectS1M1::resizeEvent(QResizeEvent* e)
{
    Q_UNUSED(e);
    // W
    float wPixelPerRatio=size().width() / 100.0;
    int wSpace1=wPixelPerRatio * 13;
    int wMeter=wPixelPerRatio * 32;
    int wSpace2=wPixelPerRatio * 6;
    int wButtonGroup1=wPixelPerRatio * 42;
    int xMeter=wSpace1;
    int xButtonGroup1=wSpace1 + wMeter + wSpace2;
    // H
    float hPixelPerRatio = size().height() / 100.0;
    int hLineEdit=hPixelPerRatio * 8;
    int hSpace1=hPixelPerRatio * 0;
    int hMeter=hPixelPerRatio * 58;
    int hSpace2=hPixelPerRatio * 1;
    int hDial=hPixelPerRatio * 18;
    int hSpace3=hPixelPerRatio * 4;
    int hButtonGroup2=hPixelPerRatio * 6;
    int hPushButtonClose=hLineEdit / 100.0 * 50;
    ui->lineEdit->setGeometry(0, 0, size().width(), hLineEdit);
    ui->pushButtonClose->setGeometry(size().width() - hPushButtonClose * 1.3, (hLineEdit - hPushButtonClose) / 2, hPushButtonClose, hPushButtonClose);
    ui->widgetVolumeMeter->setGeometry(xMeter, hLineEdit + hSpace1, wMeter, hMeter);
    ui->widgetPushButtonGroup1->setGeometry(xButtonGroup1, hLineEdit + hSpace1, wButtonGroup1, hMeter);
    ui->widgetDial->setGeometry(0, hLineEdit + hSpace1 + hMeter + hSpace2, size().width(), hDial);
    ui->widgetPushButtonGroup2->setGeometry(0, hLineEdit + hSpace1 + hMeter + hSpace2 + hDial + hSpace3, size().width(), hButtonGroup2);
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, ui->lineEdit->height()) - 3);
    if(mFont.pointSize() < 8)
    {
        mFont.setPointSize(mFont.pointSize());
    }
    else if(mFont.pointSize() < 12)
    {
        mFont.setPointSize(mFont.pointSize() - 1);
    }
    else if(mFont.pointSize() < 17)
    {
        mFont.setPointSize(mFont.pointSize() - 2);
    }
    else if(mFont.pointSize() < 22)
    {
        mFont.setPointSize(mFont.pointSize() - 3);
    }
    mFont.setPointSize(mFont.pointSize() - 1);
    ui->lineEdit->setFont(mFont);
    int radius=size().width() * 0.04;
    QString style;
    style = QString("QFrame {"
                    "   background-color: #161616;"
                    "   border-radius: %1px;"
                    "}").arg(radius);
    ui->frame->setStyleSheet(style);
    style = QString("QLineEdit {"
                    "   color: rgb(161, 161, 161);"
                    "   background-color: rgb(46, 46, 46);"
                    "   border-top-left-radius: %1px; border-top-right-radius: %1px;"
                    "   selection-color: rgb(0, 121, 107);"
                    "   selection-background-color: rgb(224, 247, 250);"
                    "}").arg(radius);
    ui->lineEdit->setStyleSheet(style);
}
void EffectS1M1::updateAttribute()
{
    if(isWidgetReady())
    {
        float gain;
        if(isWidgetEnable())
        {
            if(mMuteAffectGain)
            {
                gain = ui->widgetPushButtonGroup2->getPushButtonStateMUTE() ? (mDisableGAIN) : (ui->widgetDial->getValue());
            }
            else
            {
                gain = ui->widgetDial->getValue();
            }
            if(mPreNCChannel != WorkspaceObserver::value("NCChannel").toString())
            {
                mPreNCChannel = WorkspaceObserver::value("NCChannel").toString();
                emit attributeChanged(this->objectName(), "NCChannel", mPreNCChannel);
            }
            if(mPreNCType != WorkspaceObserver::value("NCType").toString())
            {
                mPreNCType = WorkspaceObserver::value("NCType").toString();
                emit attributeChanged(this->objectName(), "NCType", mPreNCType);
            }
            if(mPreGAIN != gain)
            {
                mPreGAIN = gain;
                emit attributeChanged(this->objectName(), "GAIN", QString::number(mPreGAIN));
            }
            if(mPreMUTE != static_cast<int>(ui->widgetPushButtonGroup2->getPushButtonStateMUTE()))
            {
                mPreMUTE = static_cast<int>(ui->widgetPushButtonGroup2->getPushButtonStateMUTE());
                emit attributeChanged(this->objectName(), "MUTE", QString::number(mPreMUTE));
            }
        }
        else
        {
            if(mMuteAffectGain)
            {
                gain = mDisableGAIN;
            }
            else
            {
                gain = ui->widgetDial->getValue();
            }
            if(mPreGAIN != gain)
            {
                mPreGAIN = gain;
                emit attributeChanged(this->objectName(), "GAIN", QString::number(mPreGAIN));
            }
            if(mPreMUTE != static_cast<int>(true))
            {
                mPreMUTE = static_cast<int>(true);
                emit attributeChanged(this->objectName(), "MUTE", QString::number(mPreMUTE));
            }
        }
    }
}
void EffectS1M1::loadSettings()
{
    mPreNCChannel = "";
    mPreNCType = "";
    mPreMUTE = -2147483648;
    mPreGAIN = -2147483648;
    setWidgetReady(false);
    WorkspaceObserver::getSettings()->beginGroup(objectName());
    bool flag=WorkspaceObserver::getSettings()->contains(objectName());
    WorkspaceObserver::getSettings()->endGroup();
    if(!flag)
    {
        WorkspaceObserver::setValue(objectName(), true);
        WorkspaceObserver::setValue("ChannelName", getChannelName());
        WorkspaceObserver::setValue("NCChannel", "IN1");
        WorkspaceObserver::setValue("NCType", "OFF");
        WorkspaceObserver::setValue("GAIN", 0);
        WorkspaceObserver::setValue("MUTE", false);
    }
    ui->lineEdit->setText(WorkspaceObserver::value("ChannelName").toString());
    if(WorkspaceObserver::value("NCChannel").toString() == "IN1")
    {
        ui->widgetPushButtonGroup1->setPushButtonStateIN1(true);
    }
    else if(WorkspaceObserver::value("NCChannel").toString() == "IN2")
    {
        ui->widgetPushButtonGroup1->setPushButtonStateIN2(true);
    }
    else if(WorkspaceObserver::value("NCChannel").toString() == "IN12")
    {
        ui->widgetPushButtonGroup1->setPushButtonStateIN12(true);
    }
    else
    {
        ui->widgetPushButtonGroup1->setPushButtonStateIN1(true);
    }
    if(WorkspaceObserver::value("NCType").toString() == "OFF")
    {
        ui->widgetPushButtonGroup1->setPushButtonStateOFF(true);
    }
    else if(WorkspaceObserver::value("NCType").toString() == "NC1")
    {
        ui->widgetPushButtonGroup1->setPushButtonStateNC1(true);
    }
    else if(WorkspaceObserver::value("NCType").toString() == "NC2")
    {
        ui->widgetPushButtonGroup1->setPushButtonStateNC2(true);
    }
    else if(WorkspaceObserver::value("NCType").toString() == "NC3")
    {
        ui->widgetPushButtonGroup1->setPushButtonStateNC3(true);
    }
    else
    {
        ui->widgetPushButtonGroup1->setPushButtonStateOFF(true);
    }
    ui->widgetDial->setValue(WorkspaceObserver::value("GAIN").toFloat());
    ui->widgetPushButtonGroup2->setPushButtonStateMUTE(WorkspaceObserver::value("MUTE").toBool());
    if(isWidgetEmitAction())
    {
        emit attributeChanged(this->objectName(), "Save_ChannelName", WorkspaceObserver::value("ChannelName").toString());
        emit attributeChanged(this->objectName(), "Save_Enable", QString::number(isWidgetEnable()));
        emit attributeChanged(this->objectName(), "Save_NCChannel", WorkspaceObserver::value("NCChannel").toString());
        emit attributeChanged(this->objectName(), "Save_NCType", WorkspaceObserver::value("NCType").toString());
        emit attributeChanged(this->objectName(), "Save_GAIN", WorkspaceObserver::value("GAIN").toString());
        emit attributeChanged(this->objectName(), "Save_MUTE", QString::number(WorkspaceObserver::value("MUTE").toBool()));
    }
    setWidgetReady(true);
    updateAttribute();
}
void EffectS1M1::AppSettingsChanged(QString objectName, QString attribute, QString value)
{
    Q_UNUSED(objectName);
    Q_UNUSED(attribute);
    Q_UNUSED(value);
}


// slot
void EffectS1M1::in_mTimer_timeout()
{
    ui->pushButtonClose->show();
}
void EffectS1M1::in_widgetPushButtonGroup1_buttonStateChanged(PushButtonS1M4::ButtonID button, bool state)
{
    Q_UNUSED(state);
    switch(button)
    {
        case PushButtonS1M4::buttonIN1:
            save("NCChannel", "IN1");
            break;
        case PushButtonS1M4::buttonIN2:
            save("NCChannel", "IN2");
            break;
        case PushButtonS1M4::buttonIN12:
            save("NCChannel", "IN12");
            break;
        case PushButtonS1M4::buttonOFF:
            save("NCType", "OFF");
            break;
        case PushButtonS1M4::buttonNC1:
            save("NCType", "NC1");
            break;
        case PushButtonS1M4::buttonNC2:
            save("NCType", "NC2");
            break;
        case PushButtonS1M4::buttonNC3:
            save("NCType", "NC3");
            break;
        default:
            break;
    }
    updateAttribute();
}
void EffectS1M1::in_widgetDial_valueChanged(float value)
{
    save("GAIN", value);
    updateAttribute();
    if(mGainAffectMute && ui->widgetPushButtonGroup2->getPushButtonStateMUTE())
    {
        ui->widgetPushButtonGroup2->setPushButtonClickedMUTE(false);
    }
}
void EffectS1M1::in_widgetPushButtonGroup2_buttonStateChanged(PushButtonS1M7::ButtonID button, bool state)
{
    switch(button)
    {
        case PushButtonS1M7::buttonMUTE:
            save("MUTE", state);
            updateAttribute();
            break;
        default:
            break;
    }
}
void EffectS1M1::on_lineEdit_textChanged(const QString& arg1)
{
    QFont font=ui->lineEdit->font();
    font.setPointSize(5);
    if(!GLBFHandle.isSuitable(font, arg1, minimumWidth() - 6))
    {
        ui->lineEdit->setText(arg1.chopped(1));
    }
}
void EffectS1M1::on_lineEdit_editingFinished()
{
    if(ui->lineEdit->text().isEmpty())
    {
        ui->lineEdit->setText(getChannelName());
    }
    ui->lineEdit->clearFocus();
    save("ChannelName", ui->lineEdit->text());
}
void EffectS1M1::on_pushButtonClose_clicked()
{
    emit attributeChanged(getChannelName(), "Hide", QString::number(static_cast<int>(true)));
    ui->pushButtonClose->hide();
}


// setter & getter
void EffectS1M1::save(QAnyStringView key, const QVariant& value)
{
    WorkspaceObserver::setValue(key, value);
    if(isWidgetEmitAction())
    {
        emit attributeChanged(this->objectName(), "Save_" + key.toString(), value.typeId() == QMetaType::Bool ? (QString::number(value.toBool())) : (value.toString()));
    }
}
EffectS1M1& EffectS1M1::setName(QString name)
{
    setObjectName(name);
    WorkspaceObserver::setObserverName(name);
    AppSettingsObserver::setObserverName(name);
    return *this;
}
EffectS1M1& EffectS1M1::setFont(QFont font)
{
    mFont = font;
    ui->widgetVolumeMeter->setFont(font);
    ui->widgetPushButtonGroup1->setFont(font);
    ui->widgetDial->setFont(font);
    ui->widgetPushButtonGroup2->setFont(font);
    QResizeEvent e(size(), size());
    resizeEvent(&e);
    update();
    return *this;
}
EffectS1M1& EffectS1M1::setVolumeMeter(int value)
{
    ui->widgetVolumeMeter->setValue(value);
    return *this;
}
EffectS1M1& EffectS1M1::setVolumeMeterClear()
{
    ui->widgetVolumeMeter->setMeterClear();
    return *this;
}
EffectS1M1& EffectS1M1::setVolumeMeterSlip()
{
    ui->widgetVolumeMeter->setMeterSlip();
    return *this;
}
EffectS1M1& EffectS1M1::setGain(float value)
{
    ui->widgetDial->setValue(value);
    return *this;
}
EffectS1M1& EffectS1M1::setGainLock(bool state)
{
    ui->widgetDial->setMovable(!state);
    return *this;
}
EffectS1M1& EffectS1M1::setMuteAffectGain(bool state)
{
    mMuteAffectGain = state;
    return *this;
}
EffectS1M1& EffectS1M1::setGainAffectMute(bool state)
{
    mGainAffectMute = state;
    return *this;
}
EffectS1M1& EffectS1M1::setGainRange(float min, float max)
{
    ui->widgetDial->setRange(min, max);
    return *this;
}
EffectS1M1& EffectS1M1::setGainDefault(float value)
{
    ui->widgetDial->setDefault(value);
    return *this;
}
EffectS1M1& EffectS1M1::setGainWidgetDisable(float value)
{
    mDisableGAIN = value;
    return *this;
}
EffectS1M1& EffectS1M1::setChannelNameEditable(bool state)
{
    ui->lineEdit->setEnabled(state);
    return *this;
}
EffectS1M1& EffectS1M1::setValueNCChannel(QString channel)
{
    mPreNCChannel = channel;
    if(mPreNCChannel == "IN1")
    {
        ui->widgetPushButtonGroup1->setPushButtonStateIN1(true);
    }
    else if(mPreNCChannel == "IN2")
    {
        ui->widgetPushButtonGroup1->setPushButtonStateIN2(true);
    }
    else if(mPreNCChannel == "IN12")
    {
        ui->widgetPushButtonGroup1->setPushButtonStateIN12(true);
    }
    else
    {
        ui->widgetPushButtonGroup1->setPushButtonStateIN1(true);
    }
    save("NCChannel", mPreNCChannel);
    return *this;
}
EffectS1M1& EffectS1M1::setValueNCType(QString type)
{
    mPreNCType = type;
    if(mPreNCType == "OFF")
    {
        ui->widgetPushButtonGroup1->setPushButtonStateOFF(true);
    }
    else if(mPreNCType == "NC1")
    {
        ui->widgetPushButtonGroup1->setPushButtonStateNC1(true);
    }
    else if(mPreNCType == "NC2")
    {
        ui->widgetPushButtonGroup1->setPushButtonStateNC2(true);
    }
    else if(mPreNCType == "NC3")
    {
        ui->widgetPushButtonGroup1->setPushButtonStateNC3(true);
    }
    else
    {
        ui->widgetPushButtonGroup1->setPushButtonStateOFF(true);
    }
    save("NCType", mPreNCType);
    return *this;
}
EffectS1M1& EffectS1M1::setValueGAIN(float value)
{
    mPreGAIN = value;
    ui->widgetDial->setValue(mPreGAIN);
    save("GAIN", mPreGAIN);
    return *this;
}
EffectS1M1& EffectS1M1::setValueMUTE(bool state)
{
    mPreMUTE = state;
    ui->widgetPushButtonGroup2->setPushButtonStateMUTE(mPreMUTE);
    save("MUTE", mPreMUTE);
    return *this;
}

