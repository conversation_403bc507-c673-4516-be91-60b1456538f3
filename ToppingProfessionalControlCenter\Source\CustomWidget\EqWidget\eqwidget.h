#ifndef EQWIDGET_H
#define EQWIDGET_H

#include <QWidget>
#include <QHBoxLayout>
#include <QVBoxLayout>
#include <QLabel>
#include <QPushButton>
#include <QVector>
#include <QScrollArea>
#include <QShowEvent>
#include <QResizeEvent>
#include "eqwidgetitemdata.h"
#include "eqwidgetiem.h"
#include "comboboxs1m3.h"

class EqWidget : public QWidget
{
    Q_OBJECT

public:
    explicit EqWidget(QWidget* parent = nullptr);
    ~EqWidget();
    EqWidget& setName(QString name);
    EqWidget& setFont(QFont font);
    void setSizeFactor(double sizeFactor);
    void setItemStretch(double sizeFactor);

    void setItemCount(int count);
    int getItemCount() const { return mItems.size(); }

    void addItem();
    void removeItem(int index = -1);
    void removeAllItems();

    void setEqData(const QVector<EqWidgetItemData>& data);
    QVector<EqWidgetItemData> getEqData() const;
    
protected:
    void showEvent(QShowEvent* event) override;
    void resizeEvent(QResizeEvent* event) override;
    void adjustFontAndSize();

private:
    void setupUI();
    void updateItemIndices();
    void createDefaultItems();

    QFont mFont;

    double mSizeFactor;
    int mMainLayoutSpacing;
    int mItemsLayoutSpacing;
    QMargins mMainLayoutMargins;
    QMargins mTitleLayoutMargins;
    QMargins mScrollAreaMargins;
    int mMainLeftStretch;
    int mLabelAreaStretch;
    int mMainSpaceStretch;
    int mScrollAreaStretch;
    int mMainRightStretch;
    int mTitileTopStretch;
    int mTitleTypeStretch;
    int mTitleGainStretch;
    int mTitleFreqStretch;
    int mTitleQStretch;
    int mTitleBottomStretch;

    QHBoxLayout* mMainLayout;
    QVBoxLayout* mTitleLayout;
    QHBoxLayout* mItemsLayout;
    QScrollArea* mScrollArea;
    QWidget* mScrollWidget;
    QLabel* mTitleTypeLabel;
    QLabel* mTitleGainLabel;
    QLabel* mTitleFrequencyLabel;
    QLabel* mTitleQLabel;

    QPushButton* mAddItemButton;
    QPushButton* mRemoveItemButton;

    QVector<EqWidgetIem*> mItems;

signals:
    void itemDataChanged(int index, const EqWidgetItemData& data);
    void ItemCountChanged(int count);
    void attributeChanged(QString objectName, QString attribute, QString value);
};

#endif // EQWIDGET_H
