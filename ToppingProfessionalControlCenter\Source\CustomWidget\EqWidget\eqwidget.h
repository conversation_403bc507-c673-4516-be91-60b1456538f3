#ifndef EQUALIZERWIDGET_H
#define EQUALIZERWIDGET_H

#include <QWidget>
#include <QHBoxLayout>
#include <QVBoxLayout>
#include <QLabel>
#include <QPushButton>
#include <QCheckBox>
#include <QVector>
#include <QScrollArea>
#include <QFrame>
#include "comboboxs1m3.h"
#include "dials1m5.h"

struct EqWidgetItemData {
    QString type;
    float gain;
    float frequency;
    float qValue;
    bool enabled;

    uint8_t changeFlags;

    enum ChangeFlag : uint8_t {
        TYPE_CHANGED      = 0x01,
        GAIN_CHANGED      = 0x02,
        FREQUENCY_CHANGED = 0x04,
        QVALUE_CHANGED    = 0x08,
        ENABLED_CHANGED   = 0x10,
        ALL_CHANGED       = 0x1F
    };

    EqWidgetItemData()
        : type("high pass")
        , gain(0.0f)
        , frequency(1000.0f)
        , qValue(0.7f)
        , enabled(true)
        , changeFlags(0)
    {}

    void setChanged(ChangeFlag flag) { changeFlags |= flag; }
    void setChanged(uint8_t flags) { changeFlags |= flags; }

    void clearChanged(ChangeFlag flag) { changeFlags &= ~flag; }
    void clearAllChanged() { changeFlags = 0; }

    bool isChanged(ChangeFlag flag) const { return (changeFlags & flag) != 0; }
    bool hasAnyChanged() const { return changeFlags != 0; }
    uint8_t getChangeFlags() const { return changeFlags; }

    bool operator==(const EqWidgetItemData& other) const {
        return type == other.type &&
               gain == other.gain &&
               frequency == other.frequency &&
               qValue == other.qValue &&
               enabled == other.enabled;
    }

    bool isTypeChanged() const { return isChanged(TYPE_CHANGED); }
    bool isGainChanged() const { return isChanged(GAIN_CHANGED); }
    bool isFrequencyChanged() const { return isChanged(FREQUENCY_CHANGED); }
    bool isQValueChanged() const { return isChanged(QVALUE_CHANGED); }
    bool isEnabledChanged() const { return isChanged(ENABLED_CHANGED); }
    QVector<QPair<QString,QString>> getChangedAttributes() const {
        if(!hasAnyChanged()) return {};
        QVector<QPair<QString,QString>> changedAttributes;
        if (isTypeChanged()) changedAttributes.append({"type", type});
        if (isGainChanged()) changedAttributes.append({"gain", QString::number(gain)});
        if (isFrequencyChanged()) changedAttributes.append({"frequency", QString::number(frequency)});
        if (isQValueChanged()) changedAttributes.append({"qValue", QString::number(qValue)});
        if (isEnabledChanged()) changedAttributes.append({"enabled", QString::number(enabled)});
        return changedAttributes;
    }

    void updateType(const QString& newType) {
        if (type != newType) {
            type = newType;
            setChanged(TYPE_CHANGED);
        }
    }

    void updateGain(float newGain) {
        if (gain != newGain) {
            gain = newGain;
            setChanged(GAIN_CHANGED);
        }
    }

    void updateFrequency(float newFrequency) {
        if (frequency != newFrequency) {
            frequency = newFrequency;
            setChanged(FREQUENCY_CHANGED);
        }
    }

    void updateQValue(float newQValue) {
        if (qValue != newQValue) {
            qValue = newQValue;
            setChanged(QVALUE_CHANGED);
        }
    }

    void updateEnabled(bool newEnabled) {
        if (enabled != newEnabled) {
            enabled = newEnabled;
            setChanged(ENABLED_CHANGED);
        }
    }

    void updateData(const QString& newType, float newGain, float newFrequency,
                   float newQValue, bool newEnabled) {
        updateType(newType);
        updateGain(newGain);
        updateFrequency(newFrequency);
        updateQValue(newQValue);
        updateEnabled(newEnabled);
    }
};

class EqWidgetIem : public QFrame
{
    Q_OBJECT

public:
    explicit EqWidgetIem(int index, QWidget* parent = nullptr);
    ~EqWidgetIem();

    EqWidgetIem& setFont(QFont font);
    void setSizeFactor(double sizeFactor);
    void setItemData(const EqWidgetItemData& data);
    EqWidgetItemData getItemData() const;

    void setItemIndex(int index);
    int getItemIndex() const { return mIndex; }

    void setMinimumItemWidth(int width);

protected:
    void resizeEvent(QResizeEvent* event) override;
    void adjustFontAndSize();

private:
    void setupUI();
    void setupConnections();

    int mIndex;
    EqWidgetItemData mData;

    QFont mFont;

    int mLayoutSpacing;
    QMargins mLayoutMargins;
    int mMinimumWidth;
    int mLabelStretch;
    int mComboStretch;
    int mGainStretch;
    int mFreqStretch;
    int mQStretch;
    int mCheckStretch;

    QVBoxLayout* mMainLayout;
    QLabel* mItemLabel;

    ComboBoxS1M3* mTypeComboBox;

    DialS1M5* mGainDial;
    DialS1M5* mFrequencyDial;
    DialS1M5* mQValueDial;

    QCheckBox* mEnabledCheckBox;

signals:
    void dataChanged(int index, const EqWidgetItemData& data);
};

class EqWidget : public QWidget
{
    Q_OBJECT

public:
    explicit EqWidget(QWidget* parent = nullptr);
    ~EqWidget();
    EqWidget& setName(QString name);
    EqWidget& setFont(QFont font);
    void setSizeFactor(double sizeFactor);
    void setItemStretch(double sizeFactor);

    void setItemCount(int count);
    int getItemCount() const { return mItems.size(); }

    void addItem();
    void removeItem(int index = -1);
    void removeAllItems();

    void setEqData(const QVector<EqWidgetItemData>& data);
    QVector<EqWidgetItemData> getEqData() const;
    
protected:
    void showEvent(QShowEvent* event) override;
    void resizeEvent(QResizeEvent* event) override;
    void adjustFontAndSize();

private:
    void setupUI();
    void updateItemIndices();
    void createDefaultItems();

    QFont mFont;

    double mSizeFactor;
    int mMainLayoutSpacing;
    int mItemsLayoutSpacing;
    QMargins mMainLayoutMargins;
    QMargins mTitleLayoutMargins;
    QMargins mScrollAreaMargins;
    int mMainLeftStretch;
    int mLabelAreaStretch;
    int mMainSpaceStretch;
    int mScrollAreaStretch;
    int mMainRightStretch;
    int mTitileTopStretch;
    int mTitleTypeStretch;
    int mTitleGainStretch;
    int mTitleFreqStretch;
    int mTitleQStretch;
    int mTitleBottomStretch;

    QHBoxLayout* mMainLayout;
    QVBoxLayout* mTitleLayout;
    QHBoxLayout* mItemsLayout;
    QScrollArea* mScrollArea;
    QWidget* mScrollWidget;
    QLabel* mTitleTypeLabel;
    QLabel* mTitleGainLabel;
    QLabel* mTitleFrequencyLabel;
    QLabel* mTitleQLabel;

    QPushButton* mAddItemButton;
    QPushButton* mRemoveItemButton;

    QVector<EqWidgetIem*> mItems;

signals:
    void itemDataChanged(int index, const EqWidgetItemData& data);
    void ItemCountChanged(int count);
    void attributeChanged(QString objectName, QString attribute, QString value);
};

#endif // EQUALIZERWIDGET_H
